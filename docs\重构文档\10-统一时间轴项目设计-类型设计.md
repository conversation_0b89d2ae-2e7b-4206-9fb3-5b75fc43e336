# 统一时间轴项目设计 - 核心接口类型设计（响应式重构版）

## 概述

基于"核心数据 + 行为分离"的重构方案，统一时间轴项目采用与数据源、媒体项目一致的响应式架构模式。**完全移除复杂的上下文模板系统**，改为直接基于关联媒体项目状态计算UI显示信息，彻底解决Vue3响应式支持问题。

## 重构背景

### 1. 架构一致性问题
当前系统中不同模块采用不同的架构模式：
- **数据源和媒体项目**：已采用"核心数据 + 行为分离"的响应式模式
- **时间轴项目**：仍使用传统类模式，存在响应式支持问题

### 2. Vue3响应式问题
传统类模式存在的问题：
- 类实例属性修改无法被Vue3响应式系统检测
- 依赖回调机制手动触发UI更新，容易遗漏
- 组件中无法直接使用响应式特性

### 3. 复杂的上下文模板系统问题
```typescript
// ❌ 旧设计：复杂的上下文模板系统
statusContext?: TimelineStatusContext
TIMELINE_CONTEXT_TEMPLATES.downloadStart()
TimelineContextUtils.hasProgress(context)

// 问题：
- 需要手动维护复杂的上下文对象
- 状态显示逻辑分散，容易不一致
- 增加了系统复杂度和维护成本
```

## 响应式重构方案

### 1. 核心设计理念

**简化的状态驱动架构**：
- 移除复杂的上下文模板系统
- 状态显示直接基于关联媒体项目计算
- 采用三层状态映射：数据源状态 → 媒体项目状态 → 时间轴项目状态
- 自动化的Sprite生命周期管理

### 2. 时间轴项目状态定义 - 3状态简化方案

**简化的状态定义**：

```typescript
/**
 * 统一时间轴项目状态 - 基于实际业务场景的三元模型
 */
type TimelineItemStatus =
  | 'ready'    // 完全就绪，可用于时间轴
  | 'loading'  // 正在处理中，包含下载、解析、等待
  | 'error'    // 不可用状态，包含错误、缺失、取消
```

### 3. 状态映射关系 - 3状态简化版

**统一映射** - 所有场景共用的简化映射：

```typescript
/**
 * 媒体状态到时间轴状态的自动映射
 * 基于关联媒体项目的状态自动计算时间轴项目状态
 */
const MEDIA_TO_TIMELINE_STATUS_MAP: Record<MediaStatus, TimelineItemStatus> = {
  'pending': 'loading',           // 等待开始处理
  'asyncprocessing': 'loading',   // 下载/获取中
  'webavdecoding': 'loading',     // 解析中
  'ready': 'ready',               // 完全就绪
  'error': 'error',               // 各种错误状态
  'cancelled': 'error',           // 用户取消
  'missing': 'error'              // 文件缺失
} as const
```

**响应式映射优势**：
- ✅ 状态变化自动触发Vue组件更新
- ✅ 无需记忆复杂映射规则
- ✅ 所有"未完成"统一为loading
- ✅ 状态显示信息直接基于关联媒体项目计算

## 响应式核心数据结构

### 1. 统一时间轴项目数据接口 - 响应式版本

**重构后数据接口**：采用"核心数据 + 行为分离"模式，纯响应式状态对象

```typescript
import { reactive } from 'vue'
import type { MediaType } from '@/types'

/**
 * 时间轴项目状态类型 - 3状态简化版
 */
export type TimelineItemStatus =
  | 'ready'    // 完全就绪，可用于时间轴
  | 'loading'  // 正在处理中，包含下载、解析、等待
  | 'error'    // 不可用状态，包含错误、缺失、取消

/**
 * 状态转换规则定义
 */
export const VALID_TIMELINE_TRANSITIONS: Record<TimelineItemStatus, TimelineItemStatus[]> = {
  'loading': ['ready', 'error'],
  'ready': ['loading', 'error'],
  'error': ['loading']
} as const

/**
 * 统一时间轴项目数据接口 - 纯响应式状态对象
 *
 * 设计理念：
 * - 纯数据对象，使用 reactive() 包装
 * - 移除复杂的上下文模板，状态显示直接基于关联媒体项目计算
 * - 所有状态变化自动触发Vue组件更新
 * - 与数据源、媒体项目采用一致的架构模式
 */
export interface UnifiedTimelineItemData {
  // ==================== 核心属性 ====================
  readonly id: string
  mediaItemId: string // 关联的统一媒体项目ID
  trackId?: string

  // ==================== 状态管理 ====================
  timelineStatus: TimelineItemStatus // 仅3状态：ready|loading|error

  // ==================== 媒体信息 ====================
  mediaType: MediaType | 'unknown' // 从关联的媒体项目同步

  // ==================== 时间范围 ====================
  timeRange: {
    timelineStartTime: number // 时间轴开始时间（帧数）
    timelineEndTime: number   // 时间轴结束时间（帧数）
  }

  // ==================== 基础配置 ====================
  config: BasicTimelineConfig // 静态配置信息

  // ==================== Sprite引用 ====================
  spriteId?: string // Sprite ID，由SpriteLifecycleManager管理
}
```

### 2. 响应式工厂函数

```typescript
import { reactive } from 'vue'
import { generateUUID4 } from '@/utils/idGenerator'

/**
 * 创建响应式时间轴项目数据对象
 */
export function createTimelineItemData(options: {
  mediaItemId: string
  trackId?: string
  timeRange: { timelineStartTime: number; timelineEndTime: number }
  config: BasicTimelineConfig
  mediaType?: MediaType
  initialStatus?: TimelineItemStatus
}): UnifiedTimelineItemData {
  return reactive({
    id: generateUUID4(),
    mediaItemId: options.mediaItemId,
    trackId: options.trackId,
    timelineStatus: options.initialStatus || 'loading',
    mediaType: options.mediaType || 'unknown',
    timeRange: options.timeRange,
    config: options.config,
    spriteId: undefined
  })
}
```

### 3. 基础配置接口

```typescript
/**
 * 统一的基础配置 - 静态配置信息
 *
 * 职责：定义时间轴项目的基本属性和媒体参数
 * 特点：创建时设置，很少变化，需要持久化保存
 */
export interface BasicTimelineConfig {
  name: string                           // 显示名称
  mediaConfig: GetMediaConfig<MediaType> // 媒体配置（变换、音量、缩略图等参数）
  animation?: AnimationConfig<MediaType> // 可选动画配置
}

/**
 * 重要说明：缩略图的正确位置
 *
 * thumbnailUrl 应该放在 mediaConfig 中，而不是作为 UnifiedTimelineItem 的独立属性：
 *
 * ✅ 正确做法：
 * interface VideoMediaConfig extends VisualMediaProps {
 *   thumbnailUrl?: string  // 视频缩略图
 * }
 *
 * interface ImageMediaConfig extends VisualMediaProps {
 *   thumbnailUrl?: string  // 图片缩略图（可能是压缩版本）
 * }
 *
 * ❌ 错误做法：
 * interface UnifiedTimelineItem {
 *   thumbnailUrl?: string  // 不应该放在这里
 * }
 *
 * 原因：
 * 1. thumbnailUrl 是媒体相关的属性，应该归属于媒体配置
 * 2. 只有视频和图像才有缩略图，音频没有
 * 3. 作为媒体配置的一部分，便于统一管理和持久化
 */
```

## 状态显示计算

### 1. 基于关联媒体项目的状态显示

**移除复杂上下文模板，改为直接计算**：

```typescript
import { TimelineStatusDisplayUtils } from '@/unified/timelineitem'
import { getMediaItemData } from '@/unified/mediaitem'

/**
 * 获取时间轴项目的状态显示信息
 * 直接基于关联的媒体项目状态计算，无需复杂的上下文模板
 */
function getTimelineDisplayInfo(timelineData: UnifiedTimelineItemData): StatusDisplayInfo {
  // 1. 获取关联的媒体项目
  const mediaData = getMediaItemData(timelineData.mediaItemId)

  // 2. 直接基于媒体项目状态计算显示信息
  return TimelineStatusDisplayUtils.getStatusDisplayInfo(mediaData)
}
```

### 2. 状态转换规则 - 极简3状态

**3状态合法转换** - 大大简化：

```typescript
/**
 * 极简3状态转换规则 - 基于真实业务场景
 *
 * 原6状态24种转换 -> 简化到3种转换
 * 状态显示信息直接基于关联媒体项目计算
 */
const VALID_TIMELINE_TRANSITIONS: Record<TimelineItemStatus, TimelineItemStatus[]> = {
  'loading': ['ready', 'error'],  // loading完成后变为就绪或错误
  'ready': ['loading', 'error'],  // 重新处理或出错
  'error': ['loading']            // 重试
}
```

## 设计优势总结

### 1. 架构显著简化
- **极致简化**：从6状态24种转换简化为3状态3种转换
- **逻辑统一**：所有"未完成"统一为loading，所有"不可用"统一为error
- **移除复杂性**：完全移除上下文模板系统

### 2. 开发体验飞跃
- **维护成本大幅降低**：
  - 状态枚举：6个→3个
  - 移除复杂的上下文模板管理
  - 状态显示逻辑集中化

### 3. 状态一致性保证
- **数据源驱动**：状态显示直接基于媒体项目状态计算
- **避免不一致**：无需手动维护状态显示信息
- **自动同步**：媒体项目状态变化自动反映到UI

### 4. 代码质量显著提升
- **类型安全**：编译时捕获状态相关的错误
- **智能提示**：IDE 提供准确的属性和方法提示
- **重构友好**：修改接口时 TypeScript 会提示所有需要更新的地方
- **简化测试**：状态显示逻辑集中，易于测试

---

*文档更新时间：2025-01-27*
*基于响应式重构版本：v2.0 - 移除上下文模板系统*
*关联文档：12-统一时间轴项目设计-使用示例.md*
