# 媒体类型统一设计 - 使用示例（响应式重构版）

## 概述

本文档基于"核心数据与行为分离"的重构方案，提供统一媒体类型的具体使用示例，展示响应式API设计，强调自动化处理和简洁的用户接口。基于统一异步源 + 响应式架构，所有媒体项目创建后自动开始处理，用户只需关注状态查询和必要的控制操作。

## 响应式最佳实践

### 重要变更：响应式数据流管理

**所有数据设置都通过响应式数据和行为函数进行**，以下方法已被移除：

```typescript
// ❌ 已移除的方法
// mediaItem.setUserDuration(duration, description)
// mediaItem.setServerMetadata(metadata)
// mediaItem.onStatusChanged(callback)

// ✅ 正确的替代方式：响应式数据 + 行为函数
// 1. 用户输入时长 - 直接修改响应式数据
item.duration = 1800

// 2. 服务器元数据 - 通过行为函数更新
UnifiedMediaItemActions.transitionTo(item, 'asyncprocessing', {
  type: 'async_processing',
  timestamp: Date.now(),
  source: 'server',
  reason: 'Server provided metadata'
})

// 3. 状态监听 - 使用Vue响应式
watch(
  () => item.mediaStatus,
  (newStatus, oldStatus) => {
    console.log(`媒体状态变化: ${oldStatus} → ${newStatus}`)
  }
)
```

### 响应式设计原则

1. **完美响应式**：所有状态变化自动触发UI更新
2. **类型安全**：利用 TypeScript 的联合类型确保类型安全
3. **函数式设计**：所有操作都是无状态的纯函数
4. **可追溯性**：通过Vue的响应式系统可以完整追踪所有状态变更

## 响应式API设计特点

### 1. 自动化处理
- **创建即启动**：媒体项目创建后自动开始处理，无需调用方法
- **状态驱动**：所有状态转换由数据源和管理器自动驱动
- **统一流程**：本地文件和远程文件使用相同的处理流程
- **响应式更新**：所有状态变化自动触发UI更新

### 2. 响应式函数设计
```typescript
// ✅ 行为函数（无状态操作）
UnifiedMediaItemActions.cancel(item)     // 取消处理
UnifiedMediaItemActions.retry(item)      // 重试处理

// ✅ 查询函数（无状态查询）
UnifiedMediaItemQueries.isReady(item)           // 是否已就绪
UnifiedMediaItemQueries.isProcessing(item)      // 是否正在处理
UnifiedMediaItemQueries.hasError(item)          // 是否有错误
UnifiedMediaItemQueries.getError(item)          // 获取错误信息
UnifiedMediaItemQueries.getProgress(item)       // 获取进度 (0-1)
UnifiedMediaItemQueries.getDuration(item)       // 获取时长
UnifiedMediaItemQueries.getUrl(item)           // 获取URL

// ✅ 直接访问响应式数据
item.mediaStatus    // 媒体状态
item.duration       // 时长
item.source.status  // 数据源状态
item.source.progress // 数据源进度

// ❌ 移除的方法（由响应式架构自动处理）
// mediaItem.startProcessing() // 创建时自动开始
// mediaItem.onStatusChanged() // 使用Vue响应式监听
// mediaItem.forceTransition() // 违反状态机原则
```

## 基础使用示例

### 1. 创建和初始化媒体项目（Vue组件）

```vue
<template>
  <div class="media-creator">
    <!-- 文件选择 -->
    <input type="file" @change="handleFileSelect" />

    <!-- 远程URL输入 -->
    <div class="remote-input">
      <input v-model="remoteUrl" placeholder="输入远程文件URL" />
      <input v-model.number="userDuration" placeholder="预估时长（可选）" />
      <button @click="addRemoteFile">添加远程文件</button>
    </div>

    <!-- 媒体项目列表 -->
    <div class="media-list">
      <div v-for="item in mediaItems" :key="item.id" class="media-item">
        <!-- ✅ 响应式状态显示 -->
        <div class="media-header">
          <span>{{ item.name }}</span>
          <span class="status" :class="item.mediaStatus">{{ item.mediaStatus }}</span>
        </div>

        <!-- ✅ 进度显示 -->
        <div v-if="UnifiedMediaItemQueries.isProcessing(item)" class="progress">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: (UnifiedMediaItemQueries.getProgress(item) || 0) + '%' }"
            ></div>
          </div>
          <span>{{ (UnifiedMediaItemQueries.getProgress(item) || 0).toFixed(1) }}%</span>
        </div>

        <!-- ✅ 媒体信息 -->
        <div v-if="UnifiedMediaItemQueries.isReady(item)" class="media-info">
          <p v-if="item.duration">时长: {{ formatDuration(item.duration) }}</p>
          <p v-if="getOriginalSize(item)">
            尺寸: {{ getOriginalSize(item)?.width }} x {{ getOriginalSize(item)?.height }}
          </p>
        </div>

        <!-- ✅ 错误信息 -->
        <div v-if="UnifiedMediaItemQueries.hasError(item)" class="error">
          错误: {{ UnifiedMediaItemQueries.getError(item) }}
        </div>

        <!-- ✅ 操作按钮 -->
        <div class="actions">
          <button
            v-if="UnifiedMediaItemQueries.canRetry(item)"
            @click="retryItem(item)"
          >
            重试
          </button>
          <button
            v-if="UnifiedMediaItemQueries.isProcessing(item)"
            @click="cancelItem(item)"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  createUnifiedMediaItemData,
  UnifiedMediaItemActions,
  UnifiedMediaItemQueries,
  DataSourceFactory
} from '@/Unified'

const remoteUrl = ref('')
const userDuration = ref<number>()
const mediaItems = ref<UnifiedMediaItemData[]>([])

// 处理文件选择
function handleFileSelect(event: Event) {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  // ✅ 创建响应式数据源
  const source = DataSourceFactory.createUserSelectedSource(file)

  // ✅ 创建响应式媒体项目
  const mediaItem = createUnifiedMediaItemData(
    generateUUID4(),
    file.name,
    source
  )

  mediaItems.value.push(mediaItem)

  // ✅ 开始处理（会自动监听数据源状态变化）
  startMediaProcessing(mediaItem)
}

// 添加远程文件
function addRemoteFile() {
  if (!remoteUrl.value) return

  // ✅ 创建远程数据源
  const source = DataSourceFactory.createRemoteSource(remoteUrl.value)

  // ✅ 创建媒体项目
  const mediaItem = createUnifiedMediaItemData(
    generateUUID4(),
    extractFileName(remoteUrl.value),
    source
  )

  // ✅ 设置用户预输入的时长
  if (userDuration.value) {
    mediaItem.duration = userDuration.value
  }

  mediaItems.value.push(mediaItem)
  startMediaProcessing(mediaItem)

  // 清空输入
  remoteUrl.value = ''
  userDuration.value = undefined
}

// 开始媒体处理
function startMediaProcessing(item: UnifiedMediaItemData) {
  // ✅ 监听数据源状态变化
  watch(
    () => item.source.status,
    (newStatus) => {
      handleSourceStatusChange(item, newStatus)
    },
    { immediate: true }
  )

  // ✅ 开始数据源获取
  UnifiedDataSourceActions.startAcquisition(item.source)
}

// 处理数据源状态变化
function handleSourceStatusChange(item: UnifiedMediaItemData, sourceStatus: string) {
  switch (sourceStatus) {
    case 'acquiring':
      UnifiedMediaItemActions.transitionTo(item, 'asyncprocessing')
      break
    case 'acquired':
      UnifiedMediaItemActions.transitionTo(item, 'webavdecoding')
      // 开始WebAV处理
      UnifiedMediaItemActions.startWebAVProcessing(item)
      break
    case 'error':
      UnifiedMediaItemActions.transitionTo(item, 'error')
      break
    case 'cancelled':
      UnifiedMediaItemActions.transitionTo(item, 'cancelled')
      break
  }
}

// 工具函数
function retryItem(item: UnifiedMediaItemData) {
  UnifiedMediaItemActions.retry(item)
}

function cancelItem(item: UnifiedMediaItemData) {
  UnifiedMediaItemActions.cancel(item)
}

function getOriginalSize(item: UnifiedMediaItemData) {
  return UnifiedMediaItemQueries.getOriginalSize(item)
}

function formatDuration(frames: number): string {
  const seconds = frames / 60 // 假设60fps
  return `${seconds.toFixed(1)}s`
}

function extractFileName(url: string): string {
  try {
    return new URL(url).pathname.split('/').pop() || 'remote-file'
  } catch {
    return 'remote-file'
  }
}

function generateUUID4(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c == 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}
</script>
```

### 2. 响应式媒体库管理（Vue组件）

```vue
<template>
  <div class="media-library">
    <!-- 拖拽区域 -->
    <div
      class="drop-zone"
      @drop="handleFileDrop"
      @dragover.prevent
      @dragenter.prevent
    >
      <p v-if="mediaItems.length === 0">拖拽文件到这里</p>

      <!-- 媒体项目网格 -->
      <div class="media-grid">
        <div v-for="item in mediaItems" :key="item.id" class="media-card">
          <!-- ✅ 响应式缩略图 -->
          <div class="thumbnail">
            <img
              v-if="item.webav?.thumbnailUrl"
              :src="item.webav.thumbnailUrl"
              :alt="item.name"
            />
            <div v-else class="placeholder">
              {{ getMediaTypeIcon(item.mediaType) }}
            </div>
          </div>

          <!-- ✅ 媒体信息 -->
          <div class="media-info">
            <h3>{{ item.name }}</h3>
            <p class="status" :class="item.mediaStatus">{{ item.mediaStatus }}</p>

            <!-- ✅ 进度条 -->
            <div v-if="UnifiedMediaItemQueries.isProcessing(item)" class="progress">
              <div
                class="progress-bar"
                :style="{ width: (UnifiedMediaItemQueries.getProgress(item) || 0) + '%' }"
              ></div>
            </div>

            <!-- ✅ 媒体详情 -->
            <div v-if="UnifiedMediaItemQueries.isReady(item)" class="details">
              <p v-if="item.duration">{{ formatDuration(item.duration) }}</p>
              <p v-if="getOriginalSize(item)">
                {{ getOriginalSize(item)?.width }}×{{ getOriginalSize(item)?.height }}
              </p>
            </div>

            <!-- ✅ 错误信息 -->
            <div v-if="UnifiedMediaItemQueries.hasError(item)" class="error">
              {{ UnifiedMediaItemQueries.getError(item) }}
            </div>
          </div>

          <!-- ✅ 操作按钮 -->
          <div class="actions">
            <button
              v-if="UnifiedMediaItemQueries.canRetry(item)"
              @click="retryMediaItem(item.id)"
              class="retry-btn"
            >
              重试
            </button>
            <button
              v-if="UnifiedMediaItemQueries.isProcessing(item)"
              @click="cancelMediaItem(item.id)"
              class="cancel-btn"
            >
              取消
            </button>
            <button
              v-if="UnifiedMediaItemQueries.isReady(item)"
              @click="useMediaItem(item)"
              class="use-btn"
            >
              使用
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- ✅ 统计信息 -->
    <div class="stats">
      <p>总计: {{ mediaItems.length }}</p>
      <p>就绪: {{ readyItems.length }}</p>
      <p>处理中: {{ processingItems.length }}</p>
      <p>错误: {{ errorItems.length }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const mediaItems = ref<UnifiedMediaItemData[]>([])

// ✅ 响应式计算属性
const readyItems = computed(() =>
  mediaItems.value.filter(UnifiedMediaItemQueries.isReady)
)

const processingItems = computed(() =>
  mediaItems.value.filter(UnifiedMediaItemQueries.isProcessing)
)

const errorItems = computed(() =>
  mediaItems.value.filter(UnifiedMediaItemQueries.hasError)
)

// 处理文件拖拽
function handleFileDrop(event: DragEvent) {
  event.preventDefault()
  const files = Array.from(event.dataTransfer?.files || [])

  files.forEach(file => {
    // ✅ 创建响应式数据源和媒体项目
    const source = DataSourceFactory.createUserSelectedSource(file)
    const mediaItem = createUnifiedMediaItemData(
      generateUUID4(),
      file.name,
      source
    )

    mediaItems.value.push(mediaItem)
    startMediaProcessing(mediaItem)
  })
}

// 用户操作：取消处理
function cancelMediaItem(itemId: string) {
  const item = mediaItems.value.find(item => item.id === itemId)
  if (item && UnifiedMediaItemQueries.isProcessing(item)) {
    UnifiedMediaItemActions.cancel(item)
  }
}

// 用户操作：重试处理
function retryMediaItem(itemId: string) {
  const item = mediaItems.value.find(item => item.id === itemId)
  if (item && UnifiedMediaItemQueries.canRetry(item)) {
    UnifiedMediaItemActions.retry(item)
  }
}

// 使用媒体项目
function useMediaItem(item: UnifiedMediaItemData) {
  // 添加到时间线或其他操作
  console.log('使用媒体项目:', item)
}
</script>
```

### 3. 响应式状态监听的使用

```vue
<template>
  <div class="media-status-monitor">
    <h2>媒体状态监控</h2>

    <!-- ✅ 实时统计 -->
    <div class="stats-panel">
      <div class="stat-item">
        <span class="label">总计:</span>
        <span class="value">{{ mediaItems.length }}</span>
      </div>
      <div class="stat-item">
        <span class="label">就绪:</span>
        <span class="value">{{ readyCount }}</span>
      </div>
      <div class="stat-item">
        <span class="label">处理中:</span>
        <span class="value">{{ processingCount }}</span>
      </div>
      <div class="stat-item">
        <span class="label">错误:</span>
        <span class="value">{{ errorCount }}</span>
      </div>
    </div>

    <!-- ✅ 状态变化日志 -->
    <div class="status-log">
      <h3>状态变化日志</h3>
      <div v-for="log in statusLogs" :key="log.id" class="log-entry">
        <span class="timestamp">{{ formatTime(log.timestamp) }}</span>
        <span class="item-name">{{ log.itemName }}</span>
        <span class="transition">{{ log.oldStatus }} → {{ log.newStatus }}</span>
        <span v-if="log.error" class="error">{{ log.error }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface StatusLog {
  id: string
  timestamp: number
  itemName: string
  oldStatus: MediaStatus
  newStatus: MediaStatus
  error?: string
}

const mediaItems = ref<UnifiedMediaItemData[]>([])
const statusLogs = ref<StatusLog[]>([])

// ✅ 响应式计算属性
const readyCount = computed(() =>
  mediaItems.value.filter(UnifiedMediaItemQueries.isReady).length
)

const processingCount = computed(() =>
  mediaItems.value.filter(UnifiedMediaItemQueries.isProcessing).length
)

const errorCount = computed(() =>
  mediaItems.value.filter(UnifiedMediaItemQueries.hasError).length
)

// ✅ 监听每个媒体项目的状态变化
function setupMediaItemWatcher(item: UnifiedMediaItemData) {
  watch(
    () => item.mediaStatus,
    (newStatus, oldStatus) => {
      if (oldStatus && newStatus !== oldStatus) {
        handleStatusChange(item, oldStatus, newStatus)
      }
    }
  )

  // ✅ 监听数据源进度变化
  watch(
    () => item.source.progress,
    (newProgress, oldProgress) => {
      if (newProgress !== oldProgress && UnifiedMediaItemQueries.isProcessing(item)) {
        handleProgressUpdate(item, newProgress)
      }
    }
  )
}

// 处理状态变化
function handleStatusChange(
  item: UnifiedMediaItemData,
  oldStatus: MediaStatus,
  newStatus: MediaStatus
) {
  console.log(`媒体状态变化: ${item.name} ${oldStatus} → ${newStatus}`)

  // 添加到日志
  statusLogs.value.unshift({
    id: generateUUID4(),
    timestamp: Date.now(),
    itemName: item.name,
    oldStatus,
    newStatus,
    error: UnifiedMediaItemQueries.hasError(item) ?
           UnifiedMediaItemQueries.getError(item) : undefined
  })

  // 限制日志数量
  if (statusLogs.value.length > 100) {
    statusLogs.value = statusLogs.value.slice(0, 100)
  }

  // 根据状态执行特定操作
  switch (newStatus) {
    case 'ready':
      handleMediaReady(item)
      break
    case 'error':
      handleMediaError(item)
      break
    case 'webavdecoding':
      handleWebAVDecoding(item)
      break
  }
}

// 处理进度更新
function handleProgressUpdate(item: UnifiedMediaItemData, progress: number) {
  console.log(`${item.name} 进度更新: ${progress.toFixed(1)}%`)

  // 可以在这里添加进度相关的业务逻辑
  if (DataSourceQueries.isRemoteSource(item.source)) {
    const stats = RemoteFileQueries.getDownloadStats(item.source)
    if (stats?.downloadSpeed) {
      console.log(`下载速度: ${stats.downloadSpeed}`)
    }
  }
}

// 处理媒体就绪
function handleMediaReady(item: UnifiedMediaItemData) {
  console.log(`媒体就绪: ${item.name}`)

  // 自动生成缩略图、添加到媒体库等
  if (item.webav?.thumbnailUrl) {
    console.log(`缩略图已生成: ${item.webav.thumbnailUrl}`)
  }
}

// 处理媒体错误
function handleMediaError(item: UnifiedMediaItemData) {
  const error = UnifiedMediaItemQueries.getError(item)
  console.error(`媒体处理失败: ${item.name} - ${error}`)

  // 可以在这里添加错误处理逻辑，如自动重试
  if (UnifiedMediaItemQueries.canRetry(item)) {
    console.log(`${item.name} 可以重试`)
  }
}

// 处理WebAV解析
function handleWebAVDecoding(item: UnifiedMediaItemData) {
  console.log(`开始WebAV解析: ${item.name}`)
}

// 工具函数
function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleTimeString()
}
</script>
```

## 设计优势总结

### 1. 响应式架构优势

#### 完美的Vue3集成
- **自动UI更新**：所有状态变化自动触发组件重新渲染
- **响应式计算**：使用 `computed` 自动计算统计信息
- **响应式监听**：使用 `watch` 监听状态变化，替代回调机制

#### 类型安全
- **联合类型**：确保数据源和媒体项目的类型安全
- **查询函数**：提供类型安全的状态查询
- **行为函数**：确保状态转换的合法性

### 2. 开发体验优势

#### 简化的API设计
```typescript
// ✅ 响应式设计 - 简洁直观
const isReady = computed(() => UnifiedMediaItemQueries.isReady(item))
const progress = computed(() => UnifiedMediaItemQueries.getProgress(item))

// ❌ 旧设计 - 复杂的回调
mediaItem.onStatusChanged((oldStatus, newStatus) => {
  if (newStatus === 'ready') {
    // 手动更新UI
  }
})
```

#### 统一的处理流程
- **本地文件**：瞬间完成获取 → WebAV解析 → 就绪
- **远程文件**：下载过程 → WebAV解析 → 就绪
- **所有类型**：使用相同的状态机和UI组件

### 3. 架构优势

#### 职责分离清晰
- **数据源层**：专注文件获取（下载、验证、传输）
- **媒体层**：专注文件处理（解析、就绪、错误）
- **UI层**：专注状态显示和用户交互

#### 扩展性强
- **新数据源类型**：只需实现数据接口和行为函数
- **新媒体类型**：复用现有的状态机和处理流程
- **新UI组件**：直接使用响应式数据，无需额外适配

### 4. 维护优势

#### 函数式设计
- **无状态函数**：更容易测试和调试
- **纯函数**：没有副作用，行为可预测
- **组合性强**：函数可以自由组合和复用

#### 统一的错误处理
- **响应式错误状态**：错误信息自动显示在UI中
- **统一的重试机制**：所有媒体类型使用相同的重试逻辑
- **详细的状态追踪**：完整的状态变化日志
