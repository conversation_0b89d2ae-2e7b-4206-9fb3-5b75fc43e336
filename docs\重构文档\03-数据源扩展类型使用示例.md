# 数据源扩展类型使用示例（响应式重构版）

## 概述

本文档基于"核心数据与行为分离"的重构方案，提供各种数据源类型的具体使用示例，展示如何在不同场景下创建和使用响应式数据源。

## 1. 用户选择文件场景

### 基本使用（Vue组件）

```vue
<template>
  <div>
    <input
      type="file"
      @change="handleFileSelect"
      accept="video/*,audio/*,image/*"
    />

    <div v-if="dataSource" class="file-status">
      <!-- ✅ 直接使用响应式属性 -->
      <p>文件状态: {{ dataSource.status }}</p>
      <p>进度: {{ dataSource.progress }}%</p>

      <!-- ✅ 使用查询函数 -->
      <div v-if="DataSourceQueries.isAcquiring(dataSource)" class="progress-bar">
        <div
          class="progress-fill"
          :style="{ width: dataSource.progress + '%' }"
        ></div>
      </div>

      <!-- ✅ 条件渲染自动响应 -->
      <p v-if="DataSourceQueries.isAcquired(dataSource)" class="success">
        文件验证成功！URL: {{ dataSource.url }}
      </p>

      <p v-if="DataSourceQueries.hasError(dataSource)" class="error">
        验证失败: {{ dataSource.errorMessage }}
      </p>

      <!-- ✅ 使用特定类型查询函数 -->
      <div v-if="DataSourceQueries.isUserSelectedSource(dataSource)" class="file-info">
        <p>文件名: {{ UserSelectedFileQueries.getFileInfo(dataSource).name }}</p>
        <p>文件大小: {{ formatBytes(UserSelectedFileQueries.getFileInfo(dataSource).size) }}</p>
        <p>文件类型: {{ UserSelectedFileQueries.getFileInfo(dataSource).type }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  DataSourceFactory,
  UnifiedDataSourceActions,
  DataSourceQueries,
  UserSelectedFileQueries
} from '@/Unified/DataSource'

const dataSource = ref<UserSelectedFileSourceData | null>(null)

function handleFileSelect(event: Event) {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  // ✅ 创建响应式数据源
  dataSource.value = DataSourceFactory.createUserSelectedSource(file)

  // ✅ 开始获取（UI会自动更新）
  UnifiedDataSourceActions.startAcquisition(dataSource.value)
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>
```

### 拖拽文件场景（Vue组件）

```vue
<template>
  <div
    class="drop-zone"
    @drop="handleDrop"
    @dragover.prevent
    @dragenter.prevent
  >
    <p v-if="dataSources.length === 0">拖拽文件到这里</p>

    <div v-for="source in dataSources" :key="source.id" class="file-item">
      <!-- ✅ 响应式状态显示 -->
      <div class="file-header">
        <span>{{ UserSelectedFileQueries.getFileInfo(source).name }}</span>
        <span class="status" :class="source.status">{{ source.status }}</span>
      </div>

      <!-- ✅ 进度条自动更新 -->
      <div v-if="DataSourceQueries.isAcquiring(source)" class="progress">
        <div class="progress-bar" :style="{ width: source.progress + '%' }"></div>
        <span>{{ source.progress }}%</span>
      </div>

      <!-- ✅ 错误信息显示 -->
      <p v-if="DataSourceQueries.hasError(source)" class="error">
        {{ source.errorMessage }}
      </p>

      <!-- ✅ 操作按钮 -->
      <div class="actions">
        <button
          v-if="DataSourceQueries.canRetry(source)"
          @click="retryFile(source)"
        >
          重试
        </button>
        <button
          v-if="DataSourceQueries.canCancel(source)"
          @click="cancelFile(source)"
        >
          取消
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const dataSources = ref<UserSelectedFileSourceData[]>([])

function handleDrop(event: DragEvent) {
  event.preventDefault()
  const files = Array.from(event.dataTransfer?.files || [])

  files.forEach(file => {
    // ✅ 创建响应式数据源
    const source = DataSourceFactory.createUserSelectedSource(file)
    dataSources.value.push(source)

    // ✅ 开始获取
    UnifiedDataSourceActions.startAcquisition(source)
  })
}

function retryFile(source: UserSelectedFileSourceData) {
  UnifiedDataSourceActions.retry(source)
  // ✅ UI会自动更新，无需手动触发
}

function cancelFile(source: UserSelectedFileSourceData) {
  UnifiedDataSourceActions.cancel(source)
  // ✅ UI会自动更新，无需手动触发
}
</script>
```

## 2. 远程文件场景

### 基本下载（Vue组件）

```vue
<template>
  <div class="remote-download">
    <div class="input-section">
      <input
        v-model="remoteUrl"
        placeholder="输入远程文件URL"
        @keyup.enter="startDownload"
      />
      <button @click="startDownload" :disabled="!remoteUrl">开始下载</button>
    </div>

    <div v-if="dataSource" class="download-status">
      <!-- ✅ 响应式状态显示 -->
      <h3>下载状态: {{ dataSource.status }}</h3>

      <!-- ✅ 进度显示 -->
      <div v-if="DataSourceQueries.isAcquiring(dataSource)" class="progress-section">
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: dataSource.progress + '%' }"
          ></div>
        </div>
        <p>进度: {{ dataSource.progress.toFixed(1) }}%</p>

        <!-- ✅ 使用特定类型查询函数 -->
        <div v-if="DataSourceQueries.isRemoteSource(dataSource)" class="download-stats">
          <p>下载速度: {{ dataSource.downloadSpeed || 'N/A' }}</p>
          <p>已下载: {{ formatBytes(dataSource.downloadedBytes) }}</p>
          <p>总大小: {{ formatBytes(dataSource.totalBytes) }}</p>

          <!-- ✅ 计算剩余时间 -->
          <p v-if="estimatedTime !== null">
            预计剩余: {{ RemoteFileQueries.formatTimeRemaining(estimatedTime) }}
          </p>
        </div>
      </div>

      <!-- ✅ 成功状态 -->
      <div v-if="DataSourceQueries.isAcquired(dataSource)" class="success">
        <p>✅ 下载完成！</p>
        <p>文件URL: {{ dataSource.url }}</p>
      </div>

      <!-- ✅ 错误状态 -->
      <div v-if="DataSourceQueries.hasError(dataSource)" class="error">
        <p>❌ 下载失败: {{ dataSource.errorMessage }}</p>
        <button @click="retryDownload">重试</button>
      </div>

      <!-- ✅ 取消按钮 -->
      <button
        v-if="DataSourceQueries.canCancel(dataSource)"
        @click="cancelDownload"
        class="cancel-btn"
      >
        取消下载
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  DataSourceFactory,
  UnifiedDataSourceActions,
  DataSourceQueries,
  RemoteFileQueries
} from '@/Unified/DataSource'

const remoteUrl = ref('')
const dataSource = ref<RemoteFileSourceData | null>(null)

// ✅ 响应式计算属性
const estimatedTime = computed(() => {
  if (!dataSource.value || !DataSourceQueries.isRemoteSource(dataSource.value)) {
    return null
  }
  return RemoteFileQueries.getEstimatedTimeRemaining(dataSource.value)
})

function startDownload() {
  if (!remoteUrl.value) return

  // ✅ 创建响应式数据源
  dataSource.value = DataSourceFactory.createRemoteSource(remoteUrl.value, {
    timeout: 30000,
    retryCount: 3
  })

  // ✅ 开始下载
  UnifiedDataSourceActions.startAcquisition(dataSource.value)
}

function retryDownload() {
  if (dataSource.value) {
    UnifiedDataSourceActions.retry(dataSource.value)
  }
}

function cancelDownload() {
  if (dataSource.value) {
    UnifiedDataSourceActions.cancel(dataSource.value)
  }
}
</script>
```

### 批量下载管理（Vue组件）

```vue
<template>
  <div class="batch-download">
    <div class="input-section">
      <textarea
        v-model="urlsText"
        placeholder="每行输入一个URL"
        rows="5"
      ></textarea>
      <button @click="startBatchDownload" :disabled="!urlsText.trim()">
        开始批量下载
      </button>
    </div>

    <div class="download-list">
      <div v-for="source in dataSources" :key="source.id" class="download-item">
        <!-- ✅ 响应式状态显示 -->
        <div class="download-header">
          <span class="url">{{ source.remoteUrl }}</span>
          <span class="status" :class="source.status">{{ source.status }}</span>
        </div>

        <!-- ✅ 进度条 -->
        <div v-if="DataSourceQueries.isAcquiring(source)" class="progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: source.progress + '%' }"></div>
          </div>
          <div class="stats">
            <span>{{ source.progress.toFixed(1) }}%</span>
            <span>{{ source.downloadSpeed || 'N/A' }}</span>
            <span>{{ formatBytes(source.downloadedBytes) }} / {{ formatBytes(source.totalBytes) }}</span>
          </div>
        </div>

        <!-- ✅ 错误信息 -->
        <p v-if="DataSourceQueries.hasError(source)" class="error">
          {{ source.errorMessage }}
        </p>

        <!-- ✅ 操作按钮 -->
        <div class="actions">
          <button
            v-if="DataSourceQueries.canRetry(source)"
            @click="retryDownload(source)"
          >
            重试
          </button>
          <button
            v-if="DataSourceQueries.canCancel(source)"
            @click="cancelDownload(source)"
          >
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- ✅ 批量操作 -->
    <div class="batch-actions">
      <button @click="cancelAllDownloads">取消全部</button>
      <button @click="retryFailedDownloads">重试失败项</button>
      <button @click="clearCompleted">清理已完成</button>
    </div>

    <!-- ✅ 统计信息 -->
    <div class="stats-summary">
      <p>总计: {{ dataSources.length }}</p>
      <p>进行中: {{ downloadingCount }}</p>
      <p>已完成: {{ completedCount }}</p>
      <p>失败: {{ failedCount }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const urlsText = ref('')
const dataSources = ref<RemoteFileSourceData[]>([])

// ✅ 响应式计算属性
const downloadingCount = computed(() =>
  dataSources.value.filter(DataSourceQueries.isAcquiring).length
)

const completedCount = computed(() =>
  dataSources.value.filter(DataSourceQueries.isAcquired).length
)

const failedCount = computed(() =>
  dataSources.value.filter(DataSourceQueries.hasError).length
)

function startBatchDownload() {
  const urls = urlsText.value
    .split('\n')
    .map(url => url.trim())
    .filter(url => url)

  urls.forEach(url => {
    // ✅ 创建响应式数据源
    const source = DataSourceFactory.createRemoteSource(url, {
      timeout: 30000,
      retryCount: 2
    })

    dataSources.value.push(source)

    // ✅ 开始下载（会自动排队）
    UnifiedDataSourceActions.startAcquisition(source)
  })

  urlsText.value = ''
}

function retryDownload(source: RemoteFileSourceData) {
  UnifiedDataSourceActions.retry(source)
}

function cancelDownload(source: RemoteFileSourceData) {
  UnifiedDataSourceActions.cancel(source)
}

function cancelAllDownloads() {
  dataSources.value
    .filter(DataSourceQueries.canCancel)
    .forEach(source => UnifiedDataSourceActions.cancel(source))
}

function retryFailedDownloads() {
  dataSources.value
    .filter(DataSourceQueries.hasError)
    .forEach(source => UnifiedDataSourceActions.retry(source))
}

function clearCompleted() {
  dataSources.value = dataSources.value.filter(source =>
    !DataSourceQueries.isAcquired(source)
  )
}
</script>
```

## 3. 与MediaItem集成使用

### 媒体项目创建（Vue组件）

```vue
<template>
  <div class="media-creation">
    <h2>创建媒体项目</h2>

    <!-- 文件选择区域 -->
    <div class="file-input-section">
      <input type="file" @change="handleFileSelect" multiple />
      <input
        v-model="remoteUrl"
        placeholder="或输入远程URL"
        @keyup.enter="addRemoteFile"
      />
      <button @click="addRemoteFile">添加远程文件</button>
    </div>

    <!-- 媒体项目列表 -->
    <div class="media-list">
      <div v-for="item in mediaItems" :key="item.id" class="media-item">
        <!-- ✅ 响应式媒体状态显示 -->
        <div class="media-header">
          <span>{{ item.name }}</span>
          <span class="status" :class="item.mediaStatus">{{ item.mediaStatus }}</span>
        </div>

        <!-- ✅ 数据源状态显示 -->
        <div class="source-status">
          <p>数据源: {{ item.source.type }}</p>
          <p>状态: {{ item.source.status }}</p>

          <!-- ✅ 进度显示 -->
          <div v-if="DataSourceQueries.isAcquiring(item.source)" class="progress">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: item.source.progress + '%' }"></div>
            </div>
            <span>{{ item.source.progress.toFixed(1) }}%</span>
          </div>
        </div>

        <!-- ✅ 媒体信息显示 -->
        <div v-if="UnifiedMediaItemQueries.isReady(item)" class="media-info">
          <p>时长: {{ item.duration }}秒</p>
          <p v-if="UnifiedMediaItemQueries.getOriginalSize(item)">
            尺寸: {{ UnifiedMediaItemQueries.getOriginalSize(item)?.width }} x {{ UnifiedMediaItemQueries.getOriginalSize(item)?.height }}
          </p>
        </div>

        <!-- ✅ 错误信息 -->
        <p v-if="UnifiedMediaItemQueries.hasError(item)" class="error">
          {{ UnifiedMediaItemQueries.getError(item) }}
        </p>

        <!-- ✅ 操作按钮 -->
        <div class="actions">
          <button
            v-if="UnifiedMediaItemQueries.isReady(item)"
            @click="useMediaItem(item)"
          >
            使用媒体
          </button>
          <button
            v-if="UnifiedMediaItemQueries.canRetry(item)"
            @click="retryMediaItem(item)"
          >
            重试
          </button>
          <button
            v-if="UnifiedMediaItemQueries.isProcessing(item)"
            @click="cancelMediaItem(item)"
          >
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- ✅ 统计信息 -->
    <div class="stats">
      <p>总计: {{ mediaItems.length }}</p>
      <p>就绪: {{ readyItems.length }}</p>
      <p>处理中: {{ processingItems.length }}</p>
      <p>错误: {{ errorItems.length }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  createUnifiedMediaItemData,
  UnifiedMediaItemActions,
  UnifiedMediaItemQueries,
  DataSourceFactory,
  DataSourceQueries
} from '@/Unified'

const remoteUrl = ref('')
const mediaItems = ref<UnifiedMediaItemData[]>([])

// ✅ 响应式计算属性
const readyItems = computed(() =>
  mediaItems.value.filter(UnifiedMediaItemQueries.isReady)
)

const processingItems = computed(() =>
  mediaItems.value.filter(UnifiedMediaItemQueries.isProcessing)
)

const errorItems = computed(() =>
  mediaItems.value.filter(UnifiedMediaItemQueries.hasError)
)

function handleFileSelect(event: Event) {
  const files = Array.from((event.target as HTMLInputElement).files || [])

  files.forEach(file => {
    // ✅ 创建数据源
    const source = DataSourceFactory.createUserSelectedSource(file)

    // ✅ 创建媒体项目
    const mediaItem = createUnifiedMediaItemData(
      generateUUID4(),
      file.name,
      source
    )

    mediaItems.value.push(mediaItem)

    // ✅ 开始处理
    startMediaProcessing(mediaItem)
  })
}

function addRemoteFile() {
  if (!remoteUrl.value) return

  // ✅ 创建远程数据源
  const source = DataSourceFactory.createRemoteSource(remoteUrl.value)

  // ✅ 创建媒体项目
  const mediaItem = createUnifiedMediaItemData(
    generateUUID4(),
    extractFileName(remoteUrl.value),
    source
  )

  mediaItems.value.push(mediaItem)

  // ✅ 开始处理
  startMediaProcessing(mediaItem)

  remoteUrl.value = ''
}

function startMediaProcessing(item: UnifiedMediaItemData) {
  // ✅ 监听数据源状态变化
  watch(
    () => item.source.status,
    (newStatus) => {
      UnifiedMediaItemActions.handleSourceStatusChange(item, item.source)
    },
    { immediate: true }
  )

  // ✅ 开始数据源获取
  UnifiedDataSourceActions.startAcquisition(item.source)
}

function useMediaItem(item: UnifiedMediaItemData) {
  // 使用媒体项目的逻辑
  console.log('使用媒体项目:', item)
}

function retryMediaItem(item: UnifiedMediaItemData) {
  UnifiedMediaItemActions.retry(item)
}

function cancelMediaItem(item: UnifiedMediaItemData) {
  UnifiedMediaItemActions.cancel(item)
}

function extractFileName(url: string): string {
  try {
    return new URL(url).pathname.split('/').pop() || 'remote-file'
  } catch {
    return 'remote-file'
  }
}
</script>
```

## 设计优势总结

### 1. 完美的响应式支持
- 所有状态变化自动触发UI更新
- 无需手动回调机制
- 支持Vue3的所有响应式特性

### 2. 类型安全
- 完整的TypeScript类型支持
- 类型守卫确保运行时安全
- 更好的IDE支持和代码提示

### 3. 函数式设计
- 所有行为都是无状态的纯函数
- 更容易测试和调试
- 支持函数组合和复用

### 4. 统一的架构
- 数据源和媒体项目采用相同的设计模式
- 降低学习成本和维护复杂度
- 便于扩展和重构

