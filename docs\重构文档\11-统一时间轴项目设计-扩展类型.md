# 统一时间轴项目设计 - 状态显示工具

## 概述

基于"核心数据 + 行为分离"的重构方案，**完全移除复杂的上下文模板系统**，改为直接基于关联媒体项目状态计算UI显示信息。本文档定义了新的状态显示工具类和相关类型。

## 设计理念

### 从复杂上下文模板到简化状态计算

**旧设计问题**：
```typescript
// ❌ 复杂的上下文模板系统
statusContext?: TimelineStatusContext
TIMELINE_CONTEXT_TEMPLATES.downloadStart()
TimelineContextUtils.hasProgress(context)

// 问题：
- 需要手动维护复杂的上下文对象
- 状态显示逻辑分散，容易不一致
- 增加了系统复杂度和维护成本
```

**新设计方案**：
```typescript
// ✅ 简化的状态显示计算
const mediaData = getMediaItemData(timelineData.mediaItemId)
const displayInfo = TimelineStatusDisplayUtils.getStatusDisplayInfo(mediaData)

// 优势：
- 状态显示直接基于数据源计算
- 逻辑集中，易于维护
- 避免状态不一致问题
```

## 状态显示工具类型定义

### 1. 状态显示信息接口

```typescript
/**
 * 状态显示信息接口
 * 包含UI显示所需的所有信息
 */
export interface StatusDisplayInfo {
  text: string              // 状态显示文本
  hasProgress: boolean      // 是否有进度信息
  percent: number           // 进度百分比 (0-100)
  speed?: string           // 下载/处理速度
  hasError: boolean        // 是否有错误
  errorMessage?: string    // 错误消息
  recoverable?: boolean    // 错误是否可恢复
}
```

### 2. 状态显示工具类

```typescript
/**
 * 时间轴状态显示工具类
 * 提供基于媒体项目状态的UI显示计算函数
 */
export class TimelineStatusDisplayUtils {
  /**
   * 获取完整的状态显示信息
   */
  static getStatusDisplayInfo(mediaData: UnifiedMediaItemData | null): StatusDisplayInfo {
    if (!mediaData) {
      return {
        text: '未知状态',
        hasProgress: false,
        percent: 0,
        hasError: false
      }
    }

    const progressInfo = this.getProgressInfo(mediaData)
    const errorInfo = this.getErrorInfo(mediaData)

    return {
      text: this.getStatusText(mediaData),
      hasProgress: progressInfo.hasProgress,
      percent: progressInfo.percent,
      speed: progressInfo.speed,
      hasError: errorInfo.hasError,
      errorMessage: errorInfo.message,
      recoverable: errorInfo.recoverable
    }
  }

  /**
   * 获取状态显示文本
   */
  static getStatusText(mediaData: UnifiedMediaItemData): string {
    switch (mediaData.mediaStatus) {
      case 'pending':
        return '等待处理'
      case 'asyncprocessing':
        if (mediaData.source?.status === 'acquiring') {
          const progress = mediaData.source.progress || 0
          const speed = (mediaData.source.type === 'remote' && 'downloadSpeed' in mediaData.source)
            ? mediaData.source.downloadSpeed
            : undefined
          return speed ? `获取中... ${progress}% (${speed})` : `获取中... ${progress}%`
        }
        return '解析中...'
      case 'webavdecoding':
        return '解析中...'
      case 'ready':
        return '就绪'
      case 'error':
        return '错误'
      case 'cancelled':
        return '已取消'
      case 'missing':
        return '文件缺失'
      default:
        return '处理中'
    }
  }
```

  /**
   * 获取进度信息
   */
  static getProgressInfo(mediaData: UnifiedMediaItemData): {
    hasProgress: boolean
    percent: number
    speed?: string
  } {
    switch (mediaData.mediaStatus) {
      case 'asyncprocessing':
        if (mediaData.source?.status === 'acquiring') {
          const speed = (mediaData.source.type === 'remote' && 'downloadSpeed' in mediaData.source)
            ? mediaData.source.downloadSpeed
            : undefined
          return {
            hasProgress: true,
            percent: mediaData.source.progress || 0,
            speed
          }
        }
        return { hasProgress: true, percent: 50 } // 解析阶段显示50%
      case 'webavdecoding':
        return { hasProgress: true, percent: 75 } // WebAV解析阶段显示75%
      default:
        return { hasProgress: false, percent: 0 }
    }
  }

  /**
   * 获取错误信息
   */
  static getErrorInfo(mediaData: UnifiedMediaItemData): {
    hasError: boolean
    message?: string
    recoverable?: boolean
  } {
    if (mediaData.mediaStatus !== 'error') {
      return { hasError: false }
    }

    return {
      hasError: true,
      message: mediaData.source?.errorMessage || '处理失败',
      recoverable: true // 大部分错误都可以重试
    }
  }

  /**
   * 检查是否有进度信息
   */
  static hasProgress(mediaData: UnifiedMediaItemData): boolean {
    return this.getProgressInfo(mediaData).hasProgress
  }

  /**
   * 检查是否有错误
   */
  static hasError(mediaData: UnifiedMediaItemData): boolean {
    return this.getErrorInfo(mediaData).hasError
  }

  /**
   * 获取进度百分比
   */
  static getProgressPercent(mediaData: UnifiedMediaItemData): number {
    return this.getProgressInfo(mediaData).percent
  }

  /**
   * 获取错误消息
   */
  static getErrorMessage(mediaData: UnifiedMediaItemData): string | null {
    const errorInfo = this.getErrorInfo(mediaData)
    return errorInfo.hasError ? (errorInfo.message || null) : null
  }

  /**
   * 检查错误是否可恢复
   */
  static isRecoverable(mediaData: UnifiedMediaItemData): boolean {
    const errorInfo = this.getErrorInfo(mediaData)
    return errorInfo.hasError ? (errorInfo.recoverable || false) : false
  }
}
```

## 便捷的计算属性工厂函数

```typescript
/**
 * 便捷的计算属性工厂函数
 * 用于Vue组件中创建响应式的状态显示计算属性
 */
export const createStatusDisplayComputeds = (getMediaData: () => UnifiedMediaItemData | null) => {
  return {
    statusText: () => {
      const mediaData = getMediaData()
      return mediaData ? TimelineStatusDisplayUtils.getStatusText(mediaData) : '未知状态'
    },

    hasProgress: () => {
      const mediaData = getMediaData()
      return mediaData ? TimelineStatusDisplayUtils.hasProgress(mediaData) : false
    },

    progressPercent: () => {
      const mediaData = getMediaData()
      return mediaData ? TimelineStatusDisplayUtils.getProgressPercent(mediaData) : 0
    },

    progressText: () => {
      const mediaData = getMediaData()
      if (!mediaData) return ''

      const progressInfo = TimelineStatusDisplayUtils.getProgressInfo(mediaData)
      if (!progressInfo.hasProgress) return ''

      return progressInfo.speed
        ? `${progressInfo.percent}% (${progressInfo.speed})`
        : `${progressInfo.percent}%`
    },

    hasError: () => {
      const mediaData = getMediaData()
      return mediaData ? TimelineStatusDisplayUtils.hasError(mediaData) : false
    },

    errorMessage: () => {
      const mediaData = getMediaData()
      return mediaData ? TimelineStatusDisplayUtils.getErrorMessage(mediaData) : null
    },

    canRetry: () => {
      const mediaData = getMediaData()
      return mediaData ? TimelineStatusDisplayUtils.isRecoverable(mediaData) : false
    }
  }
}
```

## 使用示例

### 1. 在Vue组件中使用

```typescript
<script setup lang="ts">
import { computed } from 'vue'
import { TimelineStatusDisplayUtils } from '@/unified/timelineitem'
import { useUnifiedStore } from '@/unified/unifiedStore'

const props = defineProps<{ timelineData: UnifiedTimelineItemData }>()
const unifiedStore = useUnifiedStore()

// 基于关联媒体项目计算状态显示信息
const mediaData = computed(() => unifiedStore.getMediaItem(props.timelineData.mediaItemId))

const statusInfo = computed(() =>
  TimelineStatusDisplayUtils.getStatusDisplayInfo(mediaData.value)
)

const showProgress = computed(() => statusInfo.value.hasProgress)
const progressPercent = computed(() => statusInfo.value.percent)
const statusText = computed(() => statusInfo.value.text)
</script>
```

### 2. 使用便捷工厂函数

```typescript
<script setup lang="ts">
import { computed } from 'vue'
import { createStatusDisplayComputeds } from '@/unified/timelineitem'
import { useUnifiedStore } from '@/unified/unifiedStore'

const props = defineProps<{ timelineData: UnifiedTimelineItemData }>()
const unifiedStore = useUnifiedStore()

// 使用工厂函数创建计算属性
const statusComputeds = createStatusDisplayComputeds(() =>
  unifiedStore.getMediaItem(props.timelineData.mediaItemId)
)

const statusText = computed(statusComputeds.statusText)
const hasProgress = computed(statusComputeds.hasProgress)
const progressText = computed(statusComputeds.progressText)
</script>
```

## 设计优势总结

### 1. 架构简化
- **移除复杂性**：完全移除上下文模板系统
- **逻辑集中**：状态显示逻辑集中在工具类中
- **易于维护**：单一职责，易于理解和修改

### 2. 状态一致性
- **数据源驱动**：状态显示直接基于媒体项目状态计算
- **避免不一致**：无需手动维护状态显示信息
- **自动同步**：媒体项目状态变化自动反映到UI

### 3. 开发体验
- **类型安全**：完整的TypeScript类型支持
- **易于使用**：提供便捷的工厂函数
- **响应式友好**：与Vue3响应式系统完美集成

---

*文档更新时间：2025-01-27*
*基于响应式重构版本：v2.0 - 移除上下文模板系统*
*关联文档：10-统一时间轴项目设计-类型设计.md, 12-统一时间轴项目设计-使用示例.md*
