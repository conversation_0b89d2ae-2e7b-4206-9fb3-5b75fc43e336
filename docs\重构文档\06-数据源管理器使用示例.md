# 数据源管理器使用示例（响应式重构版）

## 概述

本文档基于"核心数据与行为分离"的重构方案，提供各种数据源管理器的具体使用示例，展示如何在实际应用中使用管理器进行任务调度和资源管理。

## 1. 用户选择文件管理器使用

### 基本使用（Vue组件）

```vue
<template>
  <div class="file-manager">
    <input
      type="file"
      multiple
      @change="handleFileSelect"
      accept="video/*,audio/*,image/*"
    />

    <div class="file-list">
      <div v-for="source in dataSources" :key="source.id" class="file-item">
        <!-- ✅ 响应式状态显示 -->
        <div class="file-header">
          <span>{{ UserSelectedFileQueries.getFileInfo(source).name }}</span>
          <span class="status" :class="source.status">{{ source.status }}</span>
        </div>

        <!-- ✅ 进度显示 -->
        <div v-if="DataSourceQueries.isAcquiring(source)" class="progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: source.progress + '%' }"></div>
          </div>
          <span>验证中...</span>
        </div>

        <!-- ✅ 成功状态 -->
        <div v-if="DataSourceQueries.isAcquired(source)" class="success">
          <p>✅ 文件验证成功</p>
          <button @click="processFile(source)">开始处理</button>
        </div>

        <!-- ✅ 错误状态 -->
        <div v-if="DataSourceQueries.hasError(source)" class="error">
          <p>❌ 验证失败: {{ source.errorMessage }}</p>
          <button @click="retryFile(source)">重试</button>
        </div>
      </div>
    </div>

    <!-- ✅ 管理器统计 -->
    <div class="manager-stats">
      <h3>管理器状态</h3>
      <p>总任务: {{ managerStats.totalTasks }}</p>
      <p>运行中: {{ managerStats.runningTasks }}</p>
      <p>已完成: {{ managerStats.completedTasks }}</p>
      <p>失败: {{ managerStats.failedTasks }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  DataSourceFactory,
  UnifiedDataSourceActions,
  DataSourceQueries,
  UserSelectedFileQueries,
  UserSelectedFileManager
} from '@/Unified/DataSource'

const dataSources = ref<UserSelectedFileSourceData[]>([])
const manager = UserSelectedFileManager.getInstance()

// ✅ 响应式管理器统计
const managerStats = ref(manager.getStats())

// 定期更新统计信息
onMounted(() => {
  setInterval(() => {
    managerStats.value = manager.getStats()
  }, 1000)
})

function handleFileSelect(event: Event) {
  const files = Array.from((event.target as HTMLInputElement).files || [])

  files.forEach(file => {
    // ✅ 创建响应式数据源
    const source = DataSourceFactory.createUserSelectedSource(file)
    dataSources.value.push(source)

    // ✅ 开始验证（管理器会自动处理）
    UnifiedDataSourceActions.startAcquisition(source)
  })
}

function processFile(source: UserSelectedFileSourceData) {
  console.log(`开始处理文件: ${source.selectedFile.name}`)
  // 添加到媒体项目或时间线
}

function retryFile(source: UserSelectedFileSourceData) {
  UnifiedDataSourceActions.retry(source)
}
</script>
```
  
  setTimeout(() => {
    document.body.removeChild(errorDiv)
  }, 5000)
}
```

### 批量文件处理

```typescript
class BatchFileProcessor {
  private userFileManager = UserSelectedFileManager.getInstance()
  private processedFiles: Map<string, ProcessResult> = new Map()

  async processBatchFiles(files: File[]): Promise<ProcessResult[]> {
    const results: ProcessResult[] = []
    
    // 创建所有数据源
    const sources = files.map(file => {
      return new UserSelectedFileSource(file, (source) => {
        this.handleFileUpdate(file.name, source)
      })
    })
    
    // 开始处理所有文件
    sources.forEach(source => source.startAcquisition())
    
    // 等待所有文件处理完成
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const completed = sources.filter(source => 
          source.getStatus() === 'acquired' || source.getStatus() === 'error'
        )
        
        if (completed.length === sources.length) {
          // 所有文件处理完成
          const results = sources.map(source => ({
            fileName: source.getSelectedFile().name,
            success: source.getStatus() === 'acquired',
            error: source.getError(),
            file: source.getFile(),
            url: source.getUrl()
          }))
          resolve(results)
        } else {
          setTimeout(checkCompletion, 100)
        }
      }
      
      checkCompletion()
    })
  }

  private handleFileUpdate(fileName: string, source: UserSelectedFileSource) {
    const status = source.getStatus()
    const progress = source.getProgress()
    
    console.log(`${fileName}: ${status} (${progress}%)`)
    
    // 更新UI
    this.updateFileProgress(fileName, status, progress)
  }

  private updateFileProgress(fileName: string, status: string, progress: number) {
    const element = document.getElementById(`file-${this.sanitizeId(fileName)}`)
    if (element) {
      element.innerHTML = `
        <div class="file-item">
          <span class="file-name">${fileName}</span>
          <span class="file-status">${status}</span>
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${progress}%"></div>
          </div>
        </div>
      `
    }
  }

  private sanitizeId(fileName: string): string {
    return fileName.replace(/[^a-zA-Z0-9]/g, '_')
  }

  // 获取管理器统计信息
  getManagerStats() {
    return this.userFileManager.getStats()
  }
}

interface ProcessResult {
  fileName: string
  success: boolean
  error?: string
  file?: File | null
  url?: string | null
}

// 使用示例
const processor = new BatchFileProcessor()

document.getElementById('batch-upload')?.addEventListener('change', async (e) => {
  const files = Array.from((e.target as HTMLInputElement).files || [])
  
  try {
    const results = await processor.processBatchFiles(files)
    
    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)
    
    console.log(`处理完成: ${successful.length} 成功, ${failed.length} 失败`)
    
    // 显示统计信息
    const stats = processor.getManagerStats()
    console.log('管理器统计:', stats)
    
  } catch (error) {
    console.error('批量处理失败:', error)
  }
})
```

## 2. 远程文件管理器使用

### 单文件下载

```typescript
class RemoteFileDownloader {
  private remoteFileManager = RemoteFileManager.getInstance()
  private activeDownloads: Map<string, RemoteFileSource> = new Map()

  async downloadFile(
    url: string,
    config?: RemoteFileConfig
  ): Promise<{ file: File; url: string }> {
    const downloadId = this.generateDownloadId(url)

    const source = new RemoteFileSource(url, {
      timeout: config?.timeout || 30000,
      headers: config?.headers || {},
      retryCount: config?.retryCount || 3,
      ...config
    }, (source) => {
      this.handleDownloadUpdate(downloadId, source)
    })

    this.activeDownloads.set(downloadId, source)

    try {
      // 开始下载
      source.startAcquisition()

      // 等待下载完成
      return new Promise((resolve, reject) => {
        const checkCompletion = () => {
          const status = source.getStatus()

          if (status === 'acquired') {
            const file = source.getAcquiredFile()!
            const objectUrl = source.getUrl()!
            resolve({ file, url: objectUrl })
          } else if (status === 'error') {
            reject(new Error(source.getError() || '下载失败'))
          } else {
            setTimeout(checkCompletion, 100)
          }
        }

        checkCompletion()
      })
    } finally {
      this.activeDownloads.delete(downloadId)
    }
  }

  private handleDownloadUpdate(downloadId: string, source: RemoteFileSource) {
    const status = source.getStatus()
    const progressElement = document.getElementById(`progress-${downloadId}`)

    switch (status) {
      case 'acquiring':
        const progress = source.getProgress() || 0
        const stats = source.getDownloadStats()

        if (progressElement) {
          progressElement.style.width = `${progress * 100}%`
          progressElement.textContent = `${Math.round(progress * 100)}%`
        }

        console.log(`下载进度 [${downloadId}]: ${Math.round(progress * 100)}% - ${stats?.downloadSpeed || '计算中...'}`)
        break

      case 'acquired':
        if (progressElement) {
          progressElement.style.width = '100%'
          progressElement.textContent = '完成'
          progressElement.classList.add('complete')
        }

        console.log(`下载完成 [${downloadId}]`)
        break

      case 'error':
        if (progressElement) {
          progressElement.classList.add('error')
          progressElement.textContent = '失败'
        }

        console.error(`下载失败 [${downloadId}]: ${source.getError()}`)
        break
    }
  }

  private generateDownloadId(url: string): string {
    return `dl-${url.split('/').pop() || 'file'}-${Date.now()}`
  }

  // 获取当前下载进度
  getDownloadProgress(downloadId: string): number | undefined {
    const source = this.activeDownloads.get(downloadId)
    return source?.getProgress()
  }

  // 取消下载
  cancelDownload(downloadId: string): boolean {
    const source = this.activeDownloads.get(downloadId)
    if (source) {
      source.cancel()
      return true
    }
    return false
  }

  // 清理已完成的任务
  cleanup() {
    this.remoteFileManager.cleanupCompletedTasks()
  }
}
```

### 批量下载

### 批量下载

```typescript
class BatchRemoteDownloader {
  private remoteFileManager = RemoteFileManager.getInstance()
  private activeDownloads: Map<string, RemoteFileSource> = new Map()

  async downloadMultipleFiles(
    urls: string[],
    config?: {
      concurrency?: number
      timeout?: number
      onProgress?: (completed: number, total: number) => void
      onItemComplete?: (url: string, result: { file: File; url: string } | Error) => void
    }
  ): Promise<Array<{ url: string; file?: File; error?: string }>> {
    const concurrency = config?.concurrency || 3
    const results: Array<{ url: string; file?: File; error?: string }> = []
    let completed = 0

    // 分批处理
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency)
      const batchPromises = batch.map(async (url) => {
        try {
          const result = await this.downloadSingleFile(url, {
            timeout: config?.timeout
          })

          results.push({ url, file: result.file })
          config?.onItemComplete?.(url, result)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error)
          results.push({ url, error: errorMessage })
          config?.onItemComplete?.(url, error as Error)
        }

        completed++
        config?.onProgress?.(completed, urls.length)
      })

      await Promise.all(batchPromises)
    }

    return results
  }

  private async downloadSingleFile(
    url: string,
    config?: { timeout?: number }
  ): Promise<{ file: File; url: string }> {
    return new Promise((resolve, reject) => {
      const source = new RemoteFileSource(url, {
        timeout: config?.timeout || 30000
      }, (source) => {
        const status = source.getStatus()

        if (status === 'acquired') {
          const file = source.getAcquiredFile()!
          const objectUrl = source.getUrl()!
          resolve({ file, url: objectUrl })
        } else if (status === 'error') {
          reject(new Error(source.getError() || '下载失败'))
        }
      })

      source.startAcquisition()
    })
  }

  // 取消所有下载
  cancelAll() {
    this.activeDownloads.forEach(source => source.cancel())
    this.activeDownloads.clear()
  }
}

// 使用示例
const batchDownloader = new BatchRemoteDownloader()

async function downloadMultipleVideos(urls: string[]) {
  console.log(`开始批量下载 ${urls.length} 个文件`)

  const results = await batchDownloader.downloadMultipleFiles(urls, {
    concurrency: 2,
    timeout: 60000,
    onProgress: (completed, total) => {
      console.log(`进度: ${completed}/${total} (${Math.round(completed/total*100)}%)`)
    },
    onItemComplete: (url, result) => {
      if (result instanceof Error) {
        console.error(`✗ 下载失败: ${url} - ${result.message}`)
      } else {
        console.log(`✓ 下载成功: ${url}`)
      }
    }
  })

  const successful = results.filter(r => r.file).length
  console.log(`批量下载完成: ${successful}/${results.length} 成功`)

  return results
}
```

这些示例展示了如何在实际应用中使用数据源管理器，包括错误处理、进度跟踪、资源管理和性能优化等方面的最佳实践。
    url: string,
    source: RemoteFileSource,
    resolve: (result: DownloadResult) => void,
    reject: (error: Error) => void
  ) {
    const status = source.getStatus()
    const progress = source.getProgress()
    const stats = source.getDownloadStats()
    
    // 更新UI
    this.updateDownloadUI(url, status, progress, stats)
    
    switch (status) {
      case 'acquiring':
        console.log(`下载中: ${url} (${progress}%)`)
        if (stats.downloadSpeed) {
          console.log(`下载速度: ${stats.downloadSpeed}`)
        }
        break
        
      case 'acquired':
        console.log(`下载完成: ${url}`)
        this.activeDownloads.delete(url)
        resolve({
          success: true,
          file: source.getFile()!,
          url: source.getUrl()!,
          stats
        })
        break
        
      case 'error':
        console.error(`下载失败: ${url} - ${source.getError()}`)
        this.activeDownloads.delete(url)
        reject(new Error(source.getError() || '下载失败'))
        break
        
      case 'cancelled':
        console.log(`下载已取消: ${url}`)
        this.activeDownloads.delete(url)
        reject(new Error('下载已取消'))
        break
    }
  }

  private updateDownloadUI(
    url: string, 
    status: string, 
    progress: number, 
    stats: any
  ) {
    const urlId = this.getUrlId(url)
    const element = document.getElementById(`download-${urlId}`)
    
    if (element) {
      element.innerHTML = `
        <div class="download-item">
          <div class="download-url">${this.truncateUrl(url)}</div>
          <div class="download-status">${status}</div>
          <div class="download-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${progress}%"></div>
            </div>
            <span class="progress-text">${progress.toFixed(1)}%</span>
          </div>
          <div class="download-stats">
            ${stats.downloadSpeed ? `速度: ${stats.downloadSpeed}` : ''}
            ${stats.totalBytes > 0 ? `大小: ${this.formatBytes(stats.totalBytes)}` : ''}
          </div>
          <div class="download-actions">
            <button onclick="cancelDownload('${url}')">取消</button>
          </div>
        </div>
      `
    }
  }

  cancelDownload(url: string) {
    const source = this.activeDownloads.get(url)
    if (source) {
      source.cancel()
    }
  }

  retryDownload(url: string) {
    const source = this.activeDownloads.get(url)
    if (source) {
      source.retry()
    }
  }

  private getUrlId(url: string): string {
    return btoa(url).replace(/[^a-zA-Z0-9]/g, '')
  }

  private truncateUrl(url: string, maxLength: number = 50): string {
    return url.length > maxLength ? url.substring(0, maxLength) + '...' : url
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取所有活跃下载
  getActiveDownloads(): string[] {
    return Array.from(this.activeDownloads.keys())
  }

  // 获取管理器统计信息
  getManagerStats() {
    return this.remoteFileManager.getStats()
  }
}

interface DownloadResult {
  success: boolean
  file: File
  url: string
  stats: any
}

// 使用示例
const downloader = new RemoteFileDownloader()

// 下载单个文件
async function downloadSingleFile() {
  try {
    const result = await downloader.downloadFile(
      'https://example.com/video.mp4',
      {
        timeout: 60000,
        retryCount: 3,
        headers: {
          'User-Agent': 'VideoEditor/1.0'
        }
      }
    )
    
    console.log('下载成功:', result)
    // 使用下载的文件
    processDownloadedFile(result.file, result.url)
    
  } catch (error) {
    console.error('下载失败:', error)
    showErrorMessage(`下载失败: ${error.message}`)
  }
}

function processDownloadedFile(file: File, url: string) {
  console.log(`处理下载的文件: ${file.name}`)
  // 添加到媒体项目
}

// 全局取消下载函数（供UI调用）
function cancelDownload(url: string) {
  downloader.cancelDownload(url)
}
```

## 4. 管理器监控和统计

### 全局管理器监控

```typescript
class ManagerMonitor {
  private registry = DataSourceManagerRegistry.getInstance()
  private updateInterval: number = 1000
  private intervalId?: number

  startMonitoring() {
    this.intervalId = window.setInterval(() => {
      this.updateStats()
    }, this.updateInterval)
  }

  stopMonitoring() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = undefined
    }
  }

  private updateStats() {
    const globalStats = this.registry.getGlobalStats()
    
    console.log('=== 管理器统计 ===')
    console.log(`总管理器数: ${globalStats.totalManagers}`)
    
    globalStats.managerStats.forEach((stats, type) => {
      console.log(`${type}:`, {
        总任务: stats.totalTasks,
        运行中: stats.runningTasks,
        已完成: stats.completedTasks,
        失败: stats.failedTasks,
        并发限制: `${stats.currentRunningTasks}/${stats.maxConcurrentTasks}`
      })
    })
    
    // 更新UI
    this.updateMonitorUI(globalStats)
  }

  private updateMonitorUI(globalStats: any) {
    const monitorElement = document.getElementById('manager-monitor')
    if (!monitorElement) return
    
    let html = '<div class="manager-stats">'
    html += `<h3>管理器监控 (${globalStats.totalManagers} 个管理器)</h3>`
    
    globalStats.managerStats.forEach((stats: any, type: string) => {
      html += `
        <div class="manager-stat-item">
          <h4>${type}</h4>
          <div class="stat-grid">
            <div>总任务: ${stats.totalTasks}</div>
            <div>运行中: ${stats.runningTasks}</div>
            <div>已完成: ${stats.completedTasks}</div>
            <div>失败: ${stats.failedTasks}</div>
            <div>并发: ${stats.currentRunningTasks}/${stats.maxConcurrentTasks}</div>
          </div>
        </div>
      `
    })
    
    html += '</div>'
    monitorElement.innerHTML = html
  }

  // 清理所有管理器的已完成任务
  cleanupAll() {
    this.registry.cleanupAllCompletedTasks()
    console.log('已清理所有管理器的已完成任务')
  }

  // 调整管理器并发设置
  adjustConcurrency(managerType: string, maxConcurrent: number) {
    const manager = this.registry.getManager(managerType)
    if (manager) {
      manager.setMaxConcurrentTasks(maxConcurrent)
      console.log(`已调整 ${managerType} 管理器并发数为 ${maxConcurrent}`)
    }
  }
}

// 使用示例
const monitor = new ManagerMonitor()

// 开始监控
monitor.startMonitoring()

// 定期清理
setInterval(() => {
  monitor.cleanupAll()
}, 60000) // 每分钟清理一次

// 根据系统负载调整并发
function adjustConcurrencyBasedOnLoad() {
  const memoryInfo = (performance as any).memory
  if (memoryInfo) {
    const memoryUsage = memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize
    
    if (memoryUsage > 0.8) {
      // 内存使用率高，降低并发
      monitor.adjustConcurrency('remote', 2)
      monitor.adjustConcurrency('user-selected', 5)
    } else if (memoryUsage < 0.5) {
      // 内存使用率低，提高并发
      monitor.adjustConcurrency('remote', 5)
      monitor.adjustConcurrency('user-selected', 10)
    }
  }
}

// 每30秒检查一次系统负载
setInterval(adjustConcurrencyBasedOnLoad, 30000)
```

这些示例展示了如何在实际应用中使用各种数据源管理器，包括错误处理、进度跟踪、资源管理和性能优化等方面的最佳实践。
