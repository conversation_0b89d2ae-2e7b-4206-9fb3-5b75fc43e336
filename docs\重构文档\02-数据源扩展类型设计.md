# 数据源扩展类型设计（响应式重构版）

## 概述

基于"核心数据与行为分离"的重构方案，实现具体的数据源类型行为函数，包括用户选择文件和远程文件两种核心类型。

## 特定类型行为函数

### 1. 用户选择文件行为函数

```typescript
/**
 * 用户选择文件特定行为函数
 */
export const UserSelectedFileActions = {
  executeAcquisition(source: UserSelectedFileSourceData): void {
    // 验证文件有效性
    if (!this.validateFile(source.selectedFile)) {
      UnifiedDataSourceActions.setError(source, '文件格式不支持')
      return
    }

    // 创建URL
    const url = URL.createObjectURL(source.selectedFile)
    UnifiedDataSourceActions.setAcquired(source, source.selectedFile, url)
  },

  validateFile(file: File): boolean {
    // 文件验证逻辑
    const supportedTypes = [
      'video/mp4', 'video/webm', 'video/ogg',
      'audio/mp3', 'audio/wav', 'audio/ogg',
      'image/jpeg', 'image/png', 'image/gif'
    ]
    return supportedTypes.includes(file.type)
  },

  // 获取文件信息
  getFileInfo(source: UserSelectedFileSourceData) {
    return {
      name: source.selectedFile.name,
      size: source.selectedFile.size,
      type: source.selectedFile.type,
      lastModified: source.selectedFile.lastModified
    }
  }
}
```

### 2. 远程文件行为函数

```typescript
/**
 * 远程文件特定行为函数
 */
export const RemoteFileActions = {
  async executeAcquisition(source: RemoteFileSourceData): Promise<void> {
    try {
      source.startTime = Date.now()

      const response = await fetch(source.remoteUrl, {
        headers: source.config.headers,
        signal: this.createAbortSignal(source.config.timeout)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await this.downloadWithProgress(response, source)
      const fileName = this.extractFileName(source.remoteUrl)
      const file = new File([blob], fileName)
      const url = URL.createObjectURL(file)

      UnifiedDataSourceActions.setAcquired(source, file, url)
    } catch (error) {
      UnifiedDataSourceActions.setError(source, error.message)
    }
  },

  async downloadWithProgress(
    response: Response,
    source: RemoteFileSourceData
  ): Promise<Blob> {
    const contentLength = response.headers.get('content-length')
    source.totalBytes = contentLength ? parseInt(contentLength) : 0

    const reader = response.body?.getReader()
    const chunks: Uint8Array[] = []

    while (true) {
      const { done, value } = await reader!.read()
      if (done) break

      chunks.push(value)
      source.downloadedBytes += value.length

      // 计算下载速度
      if (source.startTime) {
        const elapsed = (Date.now() - source.startTime) / 1000
        const speed = source.downloadedBytes / elapsed
        source.downloadSpeed = this.formatSpeed(speed)
      }

      // 更新进度
      if (source.totalBytes > 0) {
        const progress = (source.downloadedBytes / source.totalBytes) * 100
        UnifiedDataSourceActions.updateProgress(source, progress)
      }
    }

    return new Blob(chunks)
  },

  formatSpeed(bytesPerSecond: number): string {
    if (bytesPerSecond < 1024) return `${bytesPerSecond.toFixed(0)} B/s`
    if (bytesPerSecond < 1024 * 1024) return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`
    return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`
  },

  extractFileName(url: string): string {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname
      return pathname.split('/').pop() || 'download'
    } catch {
      return 'download'
    }
  },

  createAbortSignal(timeout?: number): AbortSignal | undefined {
    if (!timeout) return undefined
    const controller = new AbortController()
    setTimeout(() => controller.abort(), timeout)
    return controller.signal
  },

  // 重置下载状态
  resetDownloadState(source: RemoteFileSourceData): void {
    source.downloadedBytes = 0
    source.totalBytes = 0
    source.downloadSpeed = undefined
    source.startTime = undefined
  }
}
```

## 特定类型查询函数

### 1. 用户选择文件查询函数

```typescript
/**
 * 用户选择文件特定查询函数
 */
export const UserSelectedFileQueries = {
  // 获取选择的文件
  getSelectedFile(source: UnifiedDataSourceData): File | null {
    return DataSourceQueries.isUserSelectedSource(source) ? source.selectedFile : null
  },

  // 获取文件信息
  getFileInfo(source: UserSelectedFileSourceData) {
    return {
      name: source.selectedFile.name,
      size: source.selectedFile.size,
      type: source.selectedFile.type,
      lastModified: source.selectedFile.lastModified
    }
  },

  // 检查文件类型
  isVideoFile(source: UserSelectedFileSourceData): boolean {
    return source.selectedFile.type.startsWith('video/')
  },

  isAudioFile(source: UserSelectedFileSourceData): boolean {
    return source.selectedFile.type.startsWith('audio/')
  },

  isImageFile(source: UserSelectedFileSourceData): boolean {
    return source.selectedFile.type.startsWith('image/')
  }
}
```

### 2. 远程文件查询函数

```typescript
/**
 * 远程文件特定查询函数
 */
export const RemoteFileQueries = {
  // 获取远程URL
  getRemoteUrl(source: UnifiedDataSourceData): string | null {
    return DataSourceQueries.isRemoteSource(source) ? source.remoteUrl : null
  },

  // 获取下载统计
  getDownloadStats(source: UnifiedDataSourceData): DownloadStats | null {
    if (!DataSourceQueries.isRemoteSource(source)) return null

    return {
      downloadedBytes: source.downloadedBytes,
      totalBytes: source.totalBytes,
      downloadSpeed: source.downloadSpeed,
      startTime: source.startTime
    }
  },

  // 获取下载进度百分比
  getDownloadPercentage(source: RemoteFileSourceData): number {
    if (source.totalBytes === 0) return 0
    return (source.downloadedBytes / source.totalBytes) * 100
  },

  // 获取剩余时间估算
  getEstimatedTimeRemaining(source: RemoteFileSourceData): number | null {
    if (!source.startTime || source.totalBytes === 0 || source.downloadedBytes === 0) {
      return null
    }

    const elapsed = (Date.now() - source.startTime) / 1000
    const speed = source.downloadedBytes / elapsed
    const remaining = source.totalBytes - source.downloadedBytes

    return remaining / speed
  },

  // 格式化剩余时间
  formatTimeRemaining(seconds: number): string {
    if (seconds < 60) return `${Math.round(seconds)}秒`
    if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`
    return `${Math.round(seconds / 3600)}小时`
  }
}
```

## 扩展性设计

### 1. 云盘数据源（未来扩展）

```typescript
/**
 * 云盘数据源数据接口
 */
interface CloudFileSourceData extends BaseDataSourceData {
  type: 'cloud'
  provider: 'google-drive' | 'dropbox' | 'onedrive'
  fileId: string
  auth: {
    accessToken: string
    refreshToken?: string
    expiresAt?: number
  }
}

/**
 * 云盘数据源行为函数
 */
export const CloudFileActions = {
  async executeAcquisition(source: CloudFileSourceData): Promise<void> {
    try {
      // 检查认证是否过期
      if (this.isAuthExpired(source.auth)) {
        await this.refreshAuth(source)
      }

      const downloadUrl = await this.getDownloadUrl(source)
      const response = await fetch(downloadUrl, {
        headers: {
          'Authorization': `Bearer ${source.auth.accessToken}`
        }
      })

      if (!response.ok) {
        throw new Error(`云盘下载失败: ${response.status}`)
      }

      const blob = await response.blob()
      const fileName = await this.getFileName(source)
      const file = new File([blob], fileName)
      const url = URL.createObjectURL(file)

      UnifiedDataSourceActions.setAcquired(source, file, url)
    } catch (error) {
      UnifiedDataSourceActions.setError(source, error.message)
    }
  },

  isAuthExpired(auth: CloudFileSourceData['auth']): boolean {
    return auth.expiresAt ? Date.now() > auth.expiresAt : false
  },

  async refreshAuth(source: CloudFileSourceData): Promise<void> {
    // 刷新认证逻辑
  },

  async getDownloadUrl(source: CloudFileSourceData): Promise<string> {
    // 获取下载URL逻辑
    return ''
  },

  async getFileName(source: CloudFileSourceData): Promise<string> {
    // 获取文件名逻辑
    return 'cloud-file'
  }
}
```

### 2. 工厂函数扩展

```typescript
/**
 * 扩展工厂函数
 */
export const ExtendedDataSourceFactory = {
  ...DataSourceFactory,

  createCloudSource(
    provider: CloudFileSourceData['provider'],
    fileId: string,
    auth: CloudFileSourceData['auth']
  ): CloudFileSourceData {
    return reactive({
      id: generateUUID4(),
      type: 'cloud',
      status: 'pending',
      progress: 0,
      file: null,
      url: null,
      provider,
      fileId,
      auth
    })
  }
}

/**
 * 扩展联合类型
 */
type ExtendedDataSourceData =
  | UnifiedDataSourceData
  | CloudFileSourceData
```

## 设计特点

### 1. 响应式优先
- 所有数据源状态变化自动触发UI更新
- 无需手动回调机制
- 完美支持Vue3响应式系统

### 2. 场景区分清晰
- **用户选择文件**：直接验证File对象有效性，即时完成
- **远程文件**：支持下载进度和并发控制，异步处理

### 3. 特有功能支持
- 每种数据源都有自己的特有行为函数和查询函数
- 保持基础接口的一致性
- 支持类型安全的特有功能访问

### 4. 扩展性强
- 新增数据源类型只需：
  - 定义新的数据接口
  - 实现对应的行为函数
  - 更新联合类型定义
- 支持插件化的数据源注册

### 5. 函数式设计
- 所有行为都是无状态的纯函数
- 更容易测试和调试
- 支持函数组合和复用
