# 数据源扩展管理器类型设计（响应式重构版）

## 概述

基于DataSourceManager基础抽象类，实现具体的管理器类型，包括用户选择文件管理器和远程文件管理器。本文档展示了如何适配响应式数据源。

## 管理器适配原则

### 适配要点
1. **类型参数更新**：从 `BaseDataSource` 改为对应的 `UnifiedDataSourceData`
2. **调用方式更新**：从实例方法改为行为函数调用
3. **保持核心逻辑**：任务管理、队列控制等逻辑无需改动

## 具体管理器实现

### 1. 用户选择文件管理器（适配版）

```typescript
/**
 * 用户选择文件管理器 - 处理用户选择的文件验证
 * 适配响应式数据源
 */
class UserSelectedFileManager extends DataSourceManager<UserSelectedFileSourceData> {
  private static instance: UserSelectedFileManager

  static getInstance(): UserSelectedFileManager {
    if (!this.instance) {
      this.instance = new UserSelectedFileManager()
    }
    return this.instance
  }

  private constructor() {
    super()
    this.maxConcurrentTasks = 10 // 文件验证可以并发较多
  }

  getManagerType(): string {
    return 'user-selected'
  }

  protected async executeTask(task: AcquisitionTask<UserSelectedFileSourceData>): Promise<void> {
    const source = task.source
    const file = source.selectedFile

    try {
      // 验证文件有效性
      await this.validateFile(file, task.abortController?.signal)

      // 创建对象URL
      const url = URL.createObjectURL(file)

      // ✅ 使用行为函数替代实例方法
      UnifiedDataSourceActions.setAcquired(source, file, url)

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('任务已取消')
      }
      throw error
    }
  }

  /**
   * 验证文件有效性
   */
  private async validateFile(file: File, signal?: AbortSignal): Promise<void> {
    // 检查文件大小
    if (file.size === 0) {
      throw new Error('文件为空')
    }

    if (file.size > 10 * 1024 * 1024 * 1024) { // 10GB限制
      throw new Error('文件过大，超过10GB限制')
    }

    // 检查文件类型
    const supportedTypes = [
      'video/', 'audio/', 'image/',
      'application/octet-stream' // 允许未知类型
    ]

    const isSupported = supportedTypes.some(type => 
      file.type.startsWith(type) || file.type === type
    )

    if (!isSupported && file.type !== '') {
      throw new Error(`不支持的文件类型: ${file.type}`)
    }

    // 检查文件扩展名
    const fileName = file.name.toLowerCase()
    const supportedExtensions = [
      '.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv',
      '.mp3', '.wav', '.flac', '.aac', '.ogg',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'
    ]

    const hasValidExtension = supportedExtensions.some(ext => 
      fileName.endsWith(ext)
    )

    if (!hasValidExtension) {
      const ext = fileName.split('.').pop() || '无扩展名'
      throw new Error(`不支持的文件扩展名: .${ext}`)
    }

    // 检查是否被取消
    if (signal?.aborted) {
      throw new Error('验证已取消')
    }

    // 简单的文件头检查（可选）
    await this.validateFileHeader(file, signal)
  }

  /**
   * 验证文件头
   */
  private async validateFileHeader(file: File, signal?: AbortSignal): Promise<void> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = () => {
        if (signal?.aborted) {
          reject(new Error('验证已取消'))
          return
        }
        
        const buffer = reader.result as ArrayBuffer
        const bytes = new Uint8Array(buffer)
        
        // 检查常见的文件头
        if (this.isValidFileHeader(bytes)) {
          resolve()
        } else {
          reject(new Error('文件格式可能已损坏'))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('无法读取文件'))
      }
      
      // 只读取前16字节用于文件头检查
      const blob = file.slice(0, 16)
      reader.readAsArrayBuffer(blob)
    })
  }

  /**
   * 检查文件头是否有效
   */
  private isValidFileHeader(bytes: Uint8Array): boolean {
    // MP4文件头
    if (bytes.length >= 8) {
      const mp4Signatures = [
        [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70], // ftyp
        [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70]  // ftyp
      ]
      
      for (const sig of mp4Signatures) {
        if (this.matchesSignature(bytes, sig, 4)) {
          return true
        }
      }
    }

    // JPEG文件头
    if (bytes.length >= 2 && bytes[0] === 0xFF && bytes[1] === 0xD8) {
      return true
    }

    // PNG文件头
    if (bytes.length >= 8) {
      const pngSig = [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]
      if (this.matchesSignature(bytes, pngSig, 0)) {
        return true
      }
    }

    // 其他格式或无法识别的格式也允许通过
    return true
  }

  private matchesSignature(bytes: Uint8Array, signature: number[], offset: number): boolean {
    if (bytes.length < offset + signature.length) {
      return false
    }
    
    for (let i = 0; i < signature.length; i++) {
      if (bytes[offset + i] !== signature[i]) {
        return false
      }
    }
    
    return true
  }

  protected getMaxRetries(source: UserSelectedFileSourceData): number {
    return 1 // 文件验证通常不需要重试
  }
}
```

### 2. 远程文件管理器（适配版）

```typescript
/**
 * 远程文件管理器 - 处理网络文件下载
 * 适配响应式数据源
 */
class RemoteFileManager extends DataSourceManager<RemoteFileSourceData> {
  private static instance: RemoteFileManager

  static getInstance(): RemoteFileManager {
    if (!this.instance) {
      this.instance = new RemoteFileManager()
    }
    return this.instance
  }

  private constructor() {
    super()
    this.maxConcurrentTasks = 3 // 限制并发下载数
  }

  getManagerType(): string {
    return 'remote'
  }

  protected async executeTask(task: AcquisitionTask<RemoteFileSourceData>): Promise<void> {
    const source = task.source
    const url = source.remoteUrl
    const config = source.config

    try {
      // ✅ 设置开始时间
      source.startTime = Date.now()

      // 创建下载请求
      const response = await this.downloadFile(url, config, task.abortController?.signal)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // 获取文件信息
      const contentLength = response.headers.get('content-length')
      const contentType = response.headers.get('content-type') || 'application/octet-stream'
      const fileName = this.extractFileName(url, response)

      // 创建文件对象
      const blob = await this.readResponseWithProgress(response, contentLength, source)
      const file = new File([blob], fileName, { type: contentType })
      const objectUrl = URL.createObjectURL(file)

      // ✅ 使用行为函数替代实例方法
      UnifiedDataSourceActions.setAcquired(source, file, objectUrl)

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('下载已取消')
      }
      throw error
    }
  }

  /**
   * 下载文件
   */
  private async downloadFile(
    url: string,
    config: any,
    signal?: AbortSignal
  ): Promise<Response> {
    const headers: Record<string, string> = {
      ...config.headers
    }

    return fetch(url, {
      method: 'GET',
      headers,
      signal
    })
  }

  /**
   * 读取响应并报告进度
   */
  private async readResponseWithProgress(
    response: Response,
    contentLength: string | null,
    source: RemoteFileSourceData
  ): Promise<Blob> {
    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法读取响应流')
    }

    const chunks: Uint8Array[] = []
    let receivedLength = 0
    const totalLength = contentLength ? parseInt(contentLength, 10) : 0

    // ✅ 设置总字节数
    source.totalBytes = totalLength

    while (true) {
      const { done, value } = await reader.read()

      if (done) break

      chunks.push(value)
      receivedLength += value.length

      // ✅ 更新下载字节数
      source.downloadedBytes = receivedLength

      // ✅ 计算下载速度
      if (source.startTime) {
        const elapsed = (Date.now() - source.startTime) / 1000
        const speed = receivedLength / elapsed
        source.downloadSpeed = this.formatSpeed(speed)
      }

      // ✅ 更新进度
      if (totalLength > 0) {
        const progress = (receivedLength / totalLength) * 100
        UnifiedDataSourceActions.updateProgress(source, progress)
      }
    }

    // 合并所有块
    const allChunks = new Uint8Array(receivedLength)
    let position = 0
    for (const chunk of chunks) {
      allChunks.set(chunk, position)
      position += chunk.length
    }

    return new Blob([allChunks])
  }

  /**
   * 格式化下载速度
   */
  private formatSpeed(bytesPerSecond: number): string {
    if (bytesPerSecond < 1024) return `${bytesPerSecond.toFixed(0)} B/s`
    if (bytesPerSecond < 1024 * 1024) return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`
    return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`
  }

  /**
   * 从URL和响应中提取文件名
   */
  private extractFileName(url: string, response: Response): string {
    // 尝试从Content-Disposition头获取文件名
    const contentDisposition = response.headers.get('content-disposition')
    if (contentDisposition) {
      const match = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (match) {
        return match[1].replace(/['"]/g, '')
      }
    }

    // 从URL中提取文件名
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname
      const fileName = pathname.split('/').pop()
      if (fileName && fileName.includes('.')) {
        return fileName
      }
    } catch {
      // URL解析失败
    }

    // 默认文件名
    return 'download'
  }

  protected getMaxRetries(source: RemoteFileSourceData): number {
    return source.config.retryCount || 3
  }
}
```

## 管理器注册（无需修改）

```typescript
// 注册所有管理器
const registry = DataSourceManagerRegistry.getInstance()

registry.register('user-selected', UserSelectedFileManager.getInstance())
registry.register('remote', RemoteFileManager.getInstance())
```

## 适配总结

### 1. 主要变化
- **类型参数更新**：从 `BaseDataSource` 改为对应的响应式数据接口
- **属性访问方式**：从 `source.getXxx()` 改为 `source.xxx`
- **方法调用方式**：从 `source.setXxx()` 改为 `UnifiedDataSourceActions.setXxx(source)`

### 2. 保持不变的部分
- ✅ **任务队列管理**：队列处理、并发控制逻辑
- ✅ **错误处理机制**：重试策略、超时处理等
- ✅ **管理器注册系统**：注册和获取管理器的逻辑
- ✅ **统计和监控**：任务统计、性能监控等功能

### 3. 设计优势
- **最小化重构**：核心逻辑无需改动，只需适配调用方式
- **响应式兼容**：与响应式数据源完美配合
- **性能保持**：任务管理性能不受影响
- **扩展性强**：易于添加新的管理器类型

### 4. 场景特化
- **用户选择文件管理器**：专注文件验证和格式检查，支持高并发
- **远程文件管理器**：专注下载管理，限制并发数，支持进度报告

### 5. 错误处理
- 每种管理器都有特定的错误处理逻辑
- 支持不同的重试策略
- 详细的错误信息记录

### 6. 性能优化
- 合理的并发控制设置
- 流式下载避免内存溢出
- 进度报告不影响下载性能
