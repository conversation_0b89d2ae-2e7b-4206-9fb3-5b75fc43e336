# 数据源基础类型设计（响应式重构版）

## 概述

数据源是媒体项目获取文件的抽象层，负责处理不同来源的文件获取逻辑。本文档基于"核心数据与行为分离"的重构方案，定义了响应式的数据源基础类型系统。

## 重构背景

### 响应式问题
当前的数据源类模式存在Vue3响应式支持问题：
- 类实例属性（如 `status`、`progress`）的直接修改无法被Vue3响应式系统检测
- 依赖回调机制手动触发UI更新，容易遗漏且不可靠
- 组件中无法直接使用响应式特性

### 解决方案
采用"核心数据 + 行为分离"模式：
- **核心数据**：纯粹的响应式状态对象，使用 `reactive()` 包装
- **行为函数**：无状态的纯函数，操作数据对象
- **查询函数**：纯函数，用于状态查询和计算

## 设计理念

### 1. 职责分离
- **数据源数据**：纯响应式状态对象，存储所有状态信息
- **行为函数**：无状态函数，处理数据源操作逻辑
- **查询函数**：无状态函数，提供状态查询和计算
- **数据源管理器**：专注任务调度、并发控制、资源管理
- **媒体实例**：处理业务逻辑、状态转换

### 2. 场景区分
根据文件来源的不同特点，设计不同的数据源类型：
- **用户选择文件**：有活跃的File对象，需要验证有效性
- **远程文件**：需要下载，支持进度跟踪和并发控制

### 3. 状态映射
数据源的内部状态会映射到媒体项目的抽象状态：
```typescript
// 数据源状态 → 媒体状态映射
DataSourceData.status → UnifiedMediaItemData.mediaStatus
├── 'pending'     → 'pending'
├── 'acquiring'   → 'asyncprocessing' ✅ 关键映射
├── 'acquired'    → 'webavdecoding' (开始解析文件)
├── 'error'       → 'error'
├── 'cancelled'   → 'cancelled'
└── 'missing'     → 'missing' (仅工程文件)
```

## 核心数据结构

### 1. 基础数据源数据接口

```typescript
/**
 * 基础数据源数据接口 - 纯响应式状态对象
 */
interface BaseDataSourceData {
  readonly id: string
  readonly type: string
  status: DataSourceStatus
  progress: number
  errorMessage?: string
  taskId?: string
  file: File | null
  url: string | null
}

/**
 * 用户选择文件数据源
 */
interface UserSelectedFileSourceData extends BaseDataSourceData {
  type: 'user-selected'
  selectedFile: File
}

/**
 * 远程文件数据源
 */
interface RemoteFileSourceData extends BaseDataSourceData {
  type: 'remote'
  remoteUrl: string
  config: RemoteFileConfig
  downloadedBytes: number
  totalBytes: number
  downloadSpeed?: string
  startTime?: number
}

/**
 * 远程文件配置接口
 */
interface RemoteFileConfig {
  headers?: Record<string, string>
  timeout?: number
  retryCount?: number
  retryDelay?: number
}

/**
 * 联合类型
 */
type UnifiedDataSourceData =
  | UserSelectedFileSourceData
  | RemoteFileSourceData

/**
 * 下载统计信息
 */
interface DownloadStats {
  downloadedBytes: number
  totalBytes: number
  downloadSpeed?: string
  startTime?: number
}
```

### 2. 工厂函数

```typescript
/**
 * 数据源工厂函数 - 创建响应式数据源对象
 */
export const DataSourceFactory = {
  createUserSelectedSource(file: File): UserSelectedFileSourceData {
    return reactive({
      id: generateUUID4(),
      type: 'user-selected',
      status: 'pending',
      progress: 0,
      file: null,
      url: null,
      selectedFile: file
    })
  },

  createRemoteSource(
    remoteUrl: string,
    config: RemoteFileConfig = {}
  ): RemoteFileSourceData {
    return reactive({
      id: generateUUID4(),
      type: 'remote',
      status: 'pending',
      progress: 0,
      file: null,
      url: null,
      remoteUrl,
      config,
      downloadedBytes: 0,
      totalBytes: 0
    })
  }
}

```

## 行为函数模块

### 1. 通用数据源行为

```typescript
/**
 * 通用数据源行为函数 - 无状态操作函数
 */
export const UnifiedDataSourceActions = {
  // 开始获取
  startAcquisition(source: UnifiedDataSourceData): void {
    if (source.status !== 'pending') return

    source.taskId = generateUUID4()
    source.status = 'acquiring'
    source.progress = 0
    source.errorMessage = undefined

    // 根据类型分发到具体处理器
    switch (source.type) {
      case 'user-selected':
        UserSelectedFileActions.executeAcquisition(source)
        break
      case 'remote':
        RemoteFileActions.executeAcquisition(source)
        break
    }
  },

  // 更新进度
  updateProgress(source: UnifiedDataSourceData, progress: number): void {
    if (source.status !== 'acquiring') return
    source.progress = Math.max(0, Math.min(100, progress))
  },

  // 设置完成
  setAcquired(source: UnifiedDataSourceData, file: File, url: string): void {
    source.file = file
    source.url = url
    source.status = 'acquired'
    source.progress = 100
  },

  // 设置错误
  setError(source: UnifiedDataSourceData, message: string): void {
    source.status = 'error'
    source.errorMessage = message
  },

  // 取消获取
  cancel(source: UnifiedDataSourceData): void {
    if (source.status === 'acquiring') {
      source.status = 'cancelled'
    }
  },

  // 重试
  retry(source: UnifiedDataSourceData): void {
    if (source.status === 'error' || source.status === 'cancelled') {
      source.status = 'pending'
      source.progress = 0
      source.errorMessage = undefined
      source.taskId = undefined
    }
  }
}
```

### 2. 查询函数模块

```typescript
/**
 * 数据源查询函数 - 无状态查询函数
 */
export const DataSourceQueries = {
  // 状态查询
  isPending(source: UnifiedDataSourceData): boolean {
    return source.status === 'pending'
  },

  isAcquiring(source: UnifiedDataSourceData): boolean {
    return source.status === 'acquiring'
  },

  isAcquired(source: UnifiedDataSourceData): boolean {
    return source.status === 'acquired'
  },

  hasError(source: UnifiedDataSourceData): boolean {
    return source.status === 'error'
  },

  isCancelled(source: UnifiedDataSourceData): boolean {
    return source.status === 'cancelled'
  },

  // 类型守卫
  isUserSelectedSource(source: UnifiedDataSourceData): source is UserSelectedFileSourceData {
    return source.type === 'user-selected'
  },

  isRemoteSource(source: UnifiedDataSourceData): source is RemoteFileSourceData {
    return source.type === 'remote'
  },

  // 通用属性访问
  getType(source: UnifiedDataSourceData): string {
    return source.type
  },

  getStatus(source: UnifiedDataSourceData): DataSourceStatus {
    return source.status
  },

  getProgress(source: UnifiedDataSourceData): number {
    return source.progress
  },

  getError(source: UnifiedDataSourceData): string | undefined {
    return source.errorMessage
  },

  getTaskId(source: UnifiedDataSourceData): string | undefined {
    return source.taskId
  },

  getFile(source: UnifiedDataSourceData): File | null {
    return source.file
  },

  getUrl(source: UnifiedDataSourceData): string | null {
    return source.url
  },

  // 业务查询
  canRetry(source: UnifiedDataSourceData): boolean {
    return source.status === 'error' || source.status === 'cancelled'
  },

  canCancel(source: UnifiedDataSourceData): boolean {
    return source.status === 'acquiring'
  }
}
```

## 类型定义

```typescript
/**
 * 数据源状态类型
 */
type DataSourceStatus =
  | 'pending'     // 等待开始
  | 'acquiring'   // 获取中
  | 'acquired'    // 已获取
  | 'error'       // 错误
  | 'cancelled'   // 已取消
  | 'missing'     // 缺失（适用于所有数据源类型）

/**
 * 数据源状态到媒体状态的映射表
 */
const DATA_SOURCE_TO_MEDIA_STATUS_MAP = {
  'pending': 'pending',
  'acquiring': 'asyncprocessing',
  'acquired': 'webavdecoding',
  'error': 'error',
  'cancelled': 'cancelled',
  'missing': 'missing'
} as const
```

## 设计优势

### 1. 完美的响应式支持
- 所有数据源状态变化自动触发UI更新
- 进度条、状态显示等无需手动刷新
- 组件中可直接使用 `v-model` 等响应式特性

### 2. 统一的架构模式
- 与MediaItem采用相同的"数据+行为分离"设计
- 整个系统架构一致，降低认知负担
- 便于维护和扩展

### 3. 更好的类型安全
- 联合类型 + 类型守卫提供编译时检查
- 避免运行时类型错误
- 更好的IDE支持和代码提示

### 4. 简化的扩展机制
- 添加新数据源类型只需要：
  - 定义新的数据接口
  - 实现对应的行为函数
  - 更新联合类型定义

### 5. 无状态函数的优势
- 函数更容易测试和调试
- 避免了类实例的内存开销
- 支持函数式编程范式
