# 重构操作记录系统设计 - 使用示例

## 概述

本文档提供重构操作记录系统的具体实现示例和使用方法，展示如何基于UnifiedCommand接口实现各种操作命令，以及如何在实际应用中使用这些命令。

## 时间轴项目操作命令实现

### 1. 创建时间轴项目命令

```typescript
/**
 * 创建时间轴项目命令
 */
export class CreateTimelineItemCommand extends UnifiedTimelineCommand {
  private createdItemId?: string
  
  constructor(
    private mediaItemId: string,
    private trackId: string,
    private timeRange: { timelineStartTime: number; timelineEndTime: number },
    private config: BasicTimelineConfig,
    timelineModule: UnifiedTimelineModule,
    spriteManager: SpriteLifecycleManager
  ) {
    super(
      'timeline.create',
      [], // ID将在执行时生成
      `创建时间轴项目: ${config.name}`,
      timelineModule,
      spriteManager
    )
  }
  
  async execute(): Promise<CommandResult> {
    try {
      // 创建统一时间轴项目
      const timelineItem = await this.timelineModule.createTimelineItem({
        mediaItemId: this.mediaItemId,
        trackId: this.trackId,
        timeRange: this.timeRange,
        config: this.config
      })

      this.createdItemId = timelineItem.id
      this.targetInfo.ids = [timelineItem.id]

      // 创建sprite
      await this.spriteManager.createSprite(timelineItem)

      this.stateTransition.afterState = this.createStateSnapshot()

      return {
        success: true,
        affectedItems: [timelineItem.id],
        stateChanges: this.stateTransition.afterState
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建时间轴项目失败'
      }
    }
  }
  
  async undo(): Promise<CommandResult> {
    try {
      if (this.createdItemId) {
        // 删除sprite（如果存在）
        const timelineItem = this.timelineModule.getTimelineItem(this.createdItemId)
        if (timelineItem?.sprite) {
          await this.spriteManager.destroySprite(timelineItem)
        }
        
        // 删除时间轴项目
        await this.timelineModule.removeTimelineItem(this.createdItemId)
      }
      
      return {
        success: true,
        affectedItems: this.targetInfo.ids
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '撤销创建时间轴项目失败'
      }
    }
  }
}
```

### 2. 分割时间轴项目命令

```typescript
/**
 * 分割时间轴项目命令
 */
export class SplitTimelineItemCommand extends UnifiedTimelineCommand {
  private firstItemId?: string
  private secondItemId?: string
  
  constructor(
    originalItemId: string,
    private splitTimeFrames: number,
    timelineModule: UnifiedTimelineModule,
    spriteManager: SpriteLifecycleManager
  ) {
    super(
      'timeline.split',
      [originalItemId],
      `分割时间轴项目 (在 ${splitTimeFrames} 帧)`,
      timelineModule,
      spriteManager
    )
  }
  
  async execute(): Promise<CommandResult> {
    try {
      const originalItem = this.timelineModule.getTimelineItem(this.targetInfo.ids[0])
      if (!originalItem) {
        throw new Error('原始时间轴项目不存在')
      }
      
      // 执行分割操作
      const { firstItem, secondItem } = await this.timelineModule.splitTimelineItem(
        originalItem,
        this.splitTimeFrames
      )
      
      this.firstItemId = firstItem.id
      this.secondItemId = secondItem.id
      
      // 更新目标ID列表
      this.targetInfo.ids = [this.firstItemId, this.secondItemId]
      
      this.stateTransition.afterState = this.createStateSnapshot()
      
      return {
        success: true,
        affectedItems: [this.firstItemId, this.secondItemId],
        stateChanges: this.stateTransition.afterState
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '分割时间轴项目失败'
      }
    }
  }
  
  async undo(): Promise<CommandResult> {
    try {
      // 删除分割后的项目
      if (this.firstItemId) {
        await this.timelineModule.removeTimelineItem(this.firstItemId)
      }
      if (this.secondItemId) {
        await this.timelineModule.removeTimelineItem(this.secondItemId)
      }
      
      // 从状态快照重建原始项目
      const beforeSnapshot = this.stateTransition.beforeState.timelineItems?.[this.targetInfo.ids[0]]
      if (beforeSnapshot) {
        await this.timelineModule.restoreTimelineItemFromSnapshot(beforeSnapshot)
      }
      
      return {
        success: true,
        affectedItems: this.targetInfo.ids
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '撤销分割失败'
      }
    }
  }
}
```

## 批量操作实现

### 3. 批量命令完整实现

```typescript
/**
 * 统一批量命令完整实现
 */
export class UnifiedBatchCommand implements UnifiedCommand {
  // ... 基础属性（见类型设计文档）
  
  async execute(): Promise<CommandResult> {
    const results: CommandResult[] = []
    this.executedCommands = []
    
    try {
      // 依次执行所有子命令
      for (const command of this.subCommands) {
        const result = await command.execute()
        results.push(result)
        
        if (result.success) {
          this.executedCommands.push(command)
        } else {
          // 如果某个命令失败，回滚已执行的命令
          await this.rollbackExecutedCommands()
          throw new Error(`批量操作失败: ${result.error}`)
        }
      }
      
      // 创建合并的后置状态快照
      this.stateTransition.afterState = this.createMergedAfterStateSnapshot()
      
      return {
        success: true,
        affectedItems: this.targetInfo.ids,
        stateChanges: this.stateTransition.afterState
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量操作失败'
      }
    }
  }
  
  async undo(): Promise<CommandResult> {
    try {
      // 逆序撤销所有已执行的命令
      for (let i = this.executedCommands.length - 1; i >= 0; i--) {
        const result = await this.executedCommands[i].undo()
        if (!result.success) {
          throw new Error(`批量撤销失败: ${result.error}`)
        }
      }
      
      return {
        success: true,
        affectedItems: this.targetInfo.ids
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量撤销失败'
      }
    }
  }
  
  private async rollbackExecutedCommands(): Promise<void> {
    for (let i = this.executedCommands.length - 1; i >= 0; i--) {
      try {
        await this.executedCommands[i].undo()
      } catch (error) {
        console.error('回滚命令失败:', error)
      }
    }
    this.executedCommands = []
  }
  
  private createMergedAfterStateSnapshot(): StateSnapshot {
    const merged: StateSnapshot = {
      timelineItems: {},
      tracks: {},
      selection: undefined
    }

    for (const cmd of this.executedCommands) {
      const snapshot = cmd.stateTransition.afterState
      if (snapshot) {
        if (snapshot.timelineItems) {
          Object.assign(merged.timelineItems!, snapshot.timelineItems)
        }
        if (snapshot.tracks) {
          Object.assign(merged.tracks!, snapshot.tracks)
        }
        if (snapshot.selection) {
          merged.selection = snapshot.selection
        }
      }
    }

    return merged
  }
}
```

## 历史管理器实现

### 4. 重构历史管理器完整实现

```typescript
/**
 * 重构后的统一历史管理器完整实现
 */
export class UnifiedHistoryManager {
  private commands: UnifiedCommand[] = []
  private currentIndex = -1
  private maxHistorySize = 100

  constructor(
    private notificationManager: NotificationManager,
    private commandMerger: CommandMerger = new CommandMerger()
  ) {}

  /**
   * 执行命令并添加到历史记录
   */
  async executeCommand(command: UnifiedCommand): Promise<void> {
    try {
      const result = await command.execute()

      if (!result.success) {
        throw new Error(result.error || '命令执行失败')
      }

      // 尝试与最后一个命令合并
      if (this.canMergeWithLast(command)) {
        const lastCommand = this.commands[this.currentIndex]
        const mergedCommand = CommandMerger.merge(lastCommand, command)
        this.commands[this.currentIndex] = mergedCommand

        console.log(`🔄 命令已合并: ${mergedCommand.description}`)
      } else {
        // 清除当前位置之后的所有命令
        if (this.currentIndex < this.commands.length - 1) {
          this.commands.splice(this.currentIndex + 1)
        }

        // 添加新命令到历史记录
        this.commands.push(command)
        this.currentIndex++

        // 限制历史记录大小
        this.limitHistorySize()

        console.log(`✅ 命令已执行: ${command.description}`)
      }

      // 显示成功通知
      this.notificationManager.showSuccess(
        '操作完成',
        command.description
      )
    } catch (error) {
      console.error(`❌ 命令执行失败: ${command.description}`, error)

      this.notificationManager.showError(
        '操作失败',
        `${command.description}执行失败。${error instanceof Error ? error.message : '未知错误'}`
      )

      throw error
    }
  }

  /**
   * 撤销上一个命令
   */
  async undo(): Promise<boolean> {
    if (!this.canUndo()) {
      this.notificationManager.showWarning('无法撤销', '没有可撤销的操作')
      return false
    }

    try {
      const command = this.commands[this.currentIndex]
      const result = await command.undo()

      if (!result.success) {
        throw new Error(result.error || '撤销失败')
      }

      this.currentIndex--

      console.log(`↩️ 已撤销: ${command.description}`)
      this.notificationManager.showSuccess('撤销成功', `已撤销: ${command.description}`)

      return true
    } catch (error) {
      console.error('❌ 撤销操作失败', error)

      this.notificationManager.showError(
        '撤销失败',
        `撤销操作时发生错误。${error instanceof Error ? error.message : '未知错误'}`
      )

      return false
    }
  }

  /**
   * 重做下一个命令
   */
  async redo(): Promise<boolean> {
    if (!this.canRedo()) {
      this.notificationManager.showWarning('无法重做', '没有可重做的操作')
      return false
    }

    try {
      this.currentIndex++
      const command = this.commands[this.currentIndex]
      const result = await command.execute()

      if (!result.success) {
        this.currentIndex-- // 回滚索引
        throw new Error(result.error || '重做失败')
      }

      console.log(`↪️ 已重做: ${command.description}`)
      this.notificationManager.showSuccess('重做成功', `已重做: ${command.description}`)

      return true
    } catch (error) {
      console.error('❌ 重做操作失败', error)

      this.notificationManager.showError(
        '重做失败',
        `重做操作时发生错误。${error instanceof Error ? error.message : '未知错误'}`
      )

      return false
    }
  }

  private canMergeWithLast(command: UnifiedCommand): boolean {
    if (this.currentIndex < 0) return false

    const lastCommand = this.commands[this.currentIndex]
    return CommandMerger.canMerge(lastCommand, command)
  }

  private limitHistorySize(): void {
    if (this.commands.length > this.maxHistorySize) {
      const removeCount = this.commands.length - this.maxHistorySize
      this.commands.splice(0, removeCount)
      this.currentIndex -= removeCount
    }
  }
}
```

## 实际使用示例

### 5. 批量删除时间轴项目

```typescript
// 示例：批量删除时间轴项目
async function batchDeleteTimelineItems(
  itemIds: string[],
  historyManager: UnifiedHistoryManager,
  timelineModule: UnifiedTimelineModule,
  spriteManager: SpriteLifecycleManager
) {
  // 创建删除命令列表
  const deleteCommands = itemIds.map(id =>
    new DeleteTimelineItemCommand(id, timelineModule, spriteManager)
  )

  // 创建批量命令
  const batchCommand = new UnifiedBatchCommand(
    `批量删除 ${itemIds.length} 个时间轴项目`,
    deleteCommands
  )

  // 执行批量删除
  await historyManager.executeCommand(batchCommand)
}

// 删除时间轴项目命令实现
class DeleteTimelineItemCommand extends UnifiedTimelineCommand {
  constructor(
    itemId: string,
    timelineModule: UnifiedTimelineModule,
    spriteManager: SpriteLifecycleManager
  ) {
    super(
      'timeline.delete',
      [itemId],
      `删除时间轴项目: ${itemId}`,
      timelineModule,
      spriteManager
    )
  }

  async execute(): Promise<CommandResult> {
    try {
      const item = this.timelineModule.getTimelineItem(this.targetInfo.ids[0])
      if (!item) {
        throw new Error('时间轴项目不存在')
      }

      // 删除sprite
      if (item.sprite) {
        await this.spriteManager.destroySprite(item)
      }

      // 删除时间轴项目
      await this.timelineModule.removeTimelineItem(item.id)

      this.stateTransition.afterState = this.createStateSnapshot()

      return {
        success: true,
        affectedItems: [item.id]
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除时间轴项目失败'
      }
    }
  }

  async undo(): Promise<CommandResult> {
    try {
      // 从状态快照恢复时间轴项目
      const beforeSnapshot = this.stateTransition.beforeState.timelineItems?.[this.targetInfo.ids[0]]
      if (beforeSnapshot) {
        const restoredItem = await this.timelineModule.restoreTimelineItemFromSnapshot(beforeSnapshot)

        // 如果原来有sprite，重新创建
        if (beforeSnapshot.hasSprite) {
          await this.spriteManager.createSprite(restoredItem)
        }
      }

      return {
        success: true,
        affectedItems: this.targetInfo.ids
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '撤销删除失败'
      }
    }
  }
}
```

### 6. 智能属性更新（支持命令合并）

```typescript
// 示例：智能属性更新（支持命令合并）
async function updateTimelineItemProperty(
  itemId: string,
  property: string,
  value: any,
  historyManager: UnifiedHistoryManager,
  timelineModule: UnifiedTimelineModule
) {
  const updateCommand = new UpdateTimelineItemPropertyCommand(
    itemId,
    property,
    value,
    timelineModule
  )

  // 执行更新（如果是连续的相同属性更新，会自动合并）
  await historyManager.executeCommand(updateCommand)
}

// 属性更新命令实现（支持合并）
class UpdateTimelineItemPropertyCommand extends UnifiedTimelineCommand {
  constructor(
    itemId: string,
    private property: string,
    private newValue: any,
    timelineModule: UnifiedTimelineModule
  ) {
    super(
      'property.update',
      [itemId],
      `更新属性 ${property}: ${newValue}`,
      timelineModule,
      undefined!,
      undefined!
    )
  }

  async execute(): Promise<CommandResult> {
    try {
      const item = this.timelineModule.getTimelineItem(this.targetInfo.ids[0])
      if (!item) {
        throw new Error('时间轴项目不存在')
      }

      // 更新属性
      (item.config.mediaConfig as any)[this.property] = this.newValue

      // 更新sprite（如果存在）
      if (item.sprite) {
        await this.spriteManager.updateSprite(item)
      }

      this.stateTransition.afterState = this.createStateSnapshot()

      return {
        success: true,
        affectedItems: [item.id]
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新属性失败'
      }
    }
  }

  async undo(): Promise<CommandResult> {
    try {
      // 从状态快照恢复属性值
      const beforeSnapshot = this.stateTransition.beforeState.timelineItems?.[this.targetInfo.ids[0]]
      if (beforeSnapshot) {
        const item = this.timelineModule.getTimelineItem(this.targetInfo.ids[0])
        if (item) {
          (item.config.mediaConfig as any)[this.property] = (beforeSnapshot.config.mediaConfig as any)[this.property]

          // 更新sprite
          if (item.sprite) {
            await this.spriteManager.updateSprite(item)
          }
        }
      }

      return {
        success: true,
        affectedItems: this.targetInfo.ids
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '撤销属性更新失败'
      }
    }
  }

  // 支持命令合并
  canMergeWith(other: UnifiedCommand): boolean {
    if (!(other instanceof UpdateTimelineItemPropertyCommand)) return false
    if (this.targetInfo.ids[0] !== other.targetInfo.ids[0]) return false
    if (this.property !== other.property) return false

    // 时间窗口检查（1秒内的相同属性更新可以合并）
    const timeDiff = Math.abs(other.timestamp - this.timestamp)
    return timeDiff <= 1000
  }

  mergeWith(other: UnifiedCommand): UnifiedCommand {
    if (!(other instanceof UpdateTimelineItemPropertyCommand)) {
      throw new Error('无法合并不同类型的命令')
    }

    // 创建合并后的命令，保留第一个命令的beforeState和最后一个命令的newValue
    const mergedCommand = new UpdateTimelineItemPropertyCommand(
      this.targetInfo.ids[0],
      this.property,
      other.newValue,
      this.timelineModule
    )

    // 保留原始的beforeState
    mergedCommand.stateTransition.beforeState = this.stateTransition.beforeState
    mergedCommand.description = `更新属性 ${this.property}: ${this.newValue} → ${other.newValue}`

    return mergedCommand
  }
}
```

## 实现路径

1. **实现基础命令类**：UnifiedTimelineCommand
2. **实现具体操作命令**：创建、删除、更新等各种时间轴操作
3. **实现批量操作**：UnifiedBatchCommand和相关逻辑
4. **实现历史管理器**：UnifiedHistoryManager和命令合并
5. **集成到应用**：在UI组件中使用新的命令系统
6. **测试和优化**：确保功能正确性和性能

---

*文档创建时间：2025-01-19*
*基于重构文档版本：v1.0*
*关联文档：13-重构操作记录系统设计-类型设计.md*
