# Vue3响应式重构方案：核心数据与行为分离

## 问题背景

当前的 `UnifiedMediaItem` 类存在Vue3响应式支持问题：
- 类实例属性（如 `mediaStatus`）的直接修改无法被Vue3响应式系统检测
- 依赖回调机制手动触发UI更新，容易遗漏且不可靠
- 组件中无法直接使用 `v-model` 等响应式特性

## 解决方案：核心数据 + 行为分离

### 设计理念

将**数据存储**和**业务逻辑**完全分离：
- **核心数据**：纯粹的响应式状态对象，使用 `reactive()` 包装
- **行为函数**：无状态的纯函数，操作数据对象
- **查询函数**：纯函数，用于状态查询和计算

### 架构对比

#### 当前架构（类模式）
```typescript
// 问题：类实例不是响应式的
class UnifiedMediaItem {
  public mediaStatus: MediaStatus = 'pending'  // ❌ 修改不触发响应式
  
  transitionTo(newStatus: MediaStatus): void {
    this.mediaStatus = newStatus  // ❌ Vue3无法检测
    this.onStatusChanged?.(oldStatus, newStatus)  // ❌ 需要手动回调
  }
}
```

#### 新架构（数据+行为分离）
```typescript
// ✅ 纯数据接口，完全响应式
interface UnifiedMediaItemData {
  id: string
  name: string
  mediaStatus: MediaStatus
  mediaType: MediaType | 'unknown'
  source: UnifiedDataSource
  webav?: WebAVObjects
  duration?: number
  createdAt: string
}

// ✅ 无状态行为函数
export const UnifiedMediaItemActions = {
  transitionTo(item: UnifiedMediaItemData, newStatus: MediaStatus): void {
    if (!canTransitionTo(item.mediaStatus, newStatus)) return
    item.mediaStatus = newStatus  // ✅ 直接修改响应式对象
  }
}
```

## 详细设计

### 1. 核心数据结构

```typescript
// 主要状态接口
export interface UnifiedMediaItemData {
  // 基础信息
  readonly id: string
  name: string
  createdAt: string
  
  // 状态信息
  mediaStatus: MediaStatus
  mediaType: MediaType | 'unknown'
  
  // 数据源
  source: UnifiedDataSource
  
  // WebAV对象
  webav?: WebAVObjects
  
  // 元数据
  duration?: number
}

// 工厂函数
export function createUnifiedMediaItemData(
  id: string,
  name: string,
  source: UnifiedDataSource,
  options?: Partial<UnifiedMediaItemData>
): UnifiedMediaItemData {
  return reactive({
    id,
    name,
    createdAt: new Date().toISOString(),
    mediaStatus: 'pending' as MediaStatus,
    mediaType: 'unknown' as MediaType | 'unknown',
    source,
    ...options
  })
}
```

### 2. 行为函数模块

```typescript
// 状态转换行为
export const UnifiedMediaItemActions = {
  // 状态转换
  transitionTo(
    item: UnifiedMediaItemData, 
    newStatus: MediaStatus, 
    context?: MediaTransitionContext
  ): boolean {
    if (!UnifiedMediaItemQueries.canTransitionTo(item, newStatus)) {
      console.warn(`无效状态转换: ${item.mediaStatus} → ${newStatus}`)
      return false
    }
    
    const oldStatus = item.mediaStatus
    item.mediaStatus = newStatus
    
    // 触发全局事件（替代回调机制）
    UnifiedMediaItemEvents.emit('statusChanged', {
      item,
      oldStatus,
      newStatus,
      context
    })
    
    return true
  },

  // 开始WebAV处理
  async startWebAVProcessing(item: UnifiedMediaItemData): Promise<void> {
    if (!UnifiedMediaItemActions.transitionTo(item, 'webavdecoding')) return
    
    try {
      // WebAV处理逻辑...
      const webavObjects = await processWithWebAV(item.source)
      item.webav = webavObjects
      item.duration = webavObjects.duration
      
      UnifiedMediaItemActions.transitionTo(item, 'ready')
    } catch (error) {
      UnifiedMediaItemActions.transitionTo(item, 'error', {
        type: 'error',
        errorMessage: error.message,
        errorCode: 'WEBAV_ERROR'
      })
    }
  },

  // 取消处理
  cancel(item: UnifiedMediaItemData): void {
    if (UnifiedMediaItemQueries.isProcessing(item)) {
      // 取消数据源任务
      if (item.source && typeof item.source.cancel === 'function') {
        item.source.cancel()
      }
      UnifiedMediaItemActions.transitionTo(item, 'cancelled')
    }
  },

  // 重试处理
  retry(item: UnifiedMediaItemData): void {
    if (UnifiedMediaItemQueries.canRetry(item)) {
      UnifiedMediaItemActions.transitionTo(item, 'pending')
      // 重新开始处理流程...
    }
  }
}
```

### 3. 查询函数模块

```typescript
// 状态查询
export const UnifiedMediaItemQueries = {
  // 状态检查
  isPending(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'pending'
  },

  isReady(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'ready'
  },

  isProcessing(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'asyncprocessing' || 
           item.mediaStatus === 'webavdecoding'
  },

  hasError(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'error'
  },

  hasAnyError(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'error' || 
           item.mediaStatus === 'cancelled' || 
           item.mediaStatus === 'missing'
  },

  isParsing(item: UnifiedMediaItemData): boolean {
    return UnifiedMediaItemQueries.isPending(item) || 
           UnifiedMediaItemQueries.isProcessing(item)
  },

  // 状态转换验证
  canTransitionTo(item: UnifiedMediaItemData, newStatus: MediaStatus): boolean {
    const validTransitions: Record<MediaStatus, MediaStatus[]> = {
      pending: ['asyncprocessing', 'error', 'cancelled'],
      asyncprocessing: ['webavdecoding', 'error', 'cancelled'],
      webavdecoding: ['ready', 'error', 'cancelled'],
      ready: ['error'],
      error: ['pending', 'cancelled'],
      cancelled: ['pending'],
      missing: ['pending', 'cancelled'],
    }
    
    return validTransitions[item.mediaStatus]?.includes(newStatus) || false
  },

  // 业务查询
  canRetry(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'error' || 
           item.mediaStatus === 'cancelled' || 
           item.mediaStatus === 'missing'
  },

  getProgress(item: UnifiedMediaItemData): number | undefined {
    return item.source?.getProgress?.()
  },

  getError(item: UnifiedMediaItemData): string | undefined {
    return item.source?.getError?.()
  },

  getUrl(item: UnifiedMediaItemData): string | undefined {
    return item.source?.getUrl?.()
  },

  getOriginalSize(item: UnifiedMediaItemData): { width: number; height: number } | undefined {
    const width = item.webav?.originalWidth
    const height = item.webav?.originalHeight
    return (width !== undefined && height !== undefined) ? { width, height } : undefined
  }
}
```

## Store层重构

### 新的UnifiedMediaModule

```typescript
export function createUnifiedMediaModule() {
  // ✅ 响应式数据存储
  const mediaItems = ref<UnifiedMediaItemData[]>([])
  
  // 添加媒体项目
  function addMediaItem(itemData: UnifiedMediaItemData) {
    mediaItems.value.push(itemData)
    
    // 设置数据源状态监听
    if (itemData.source) {
      itemData.source.onUpdate = (source) => {
        handleSourceStatusChange(itemData, source)
      }
    }
  }
  
  // 处理数据源状态变化
  function handleSourceStatusChange(
    item: UnifiedMediaItemData, 
    source: UnifiedDataSource
  ) {
    const sourceStatus = source.getStatus()
    
    switch (sourceStatus) {
      case 'acquiring':
        UnifiedMediaItemActions.transitionTo(item, 'asyncprocessing')
        break
      case 'acquired':
        UnifiedMediaItemActions.transitionTo(item, 'webavdecoding')
        UnifiedMediaItemActions.startWebAVProcessing(item)
        break
      case 'error':
        UnifiedMediaItemActions.transitionTo(item, 'error', {
          type: 'error',
          errorMessage: source.getError() || '数据源获取失败'
        })
        break
      case 'cancelled':
        UnifiedMediaItemActions.transitionTo(item, 'cancelled')
        break
    }
  }
  
  // 查询方法
  function getMediaItem(id: string): UnifiedMediaItemData | undefined {
    return mediaItems.value.find(item => item.id === id)
  }
  
  function getReadyItems(): UnifiedMediaItemData[] {
    return mediaItems.value.filter(UnifiedMediaItemQueries.isReady)
  }
  
  function getProcessingItems(): UnifiedMediaItemData[] {
    return mediaItems.value.filter(UnifiedMediaItemQueries.isProcessing)
  }
  
  return {
    // 响应式状态
    mediaItems: readonly(mediaItems),
    
    // 操作方法
    addMediaItem,
    getMediaItem,
    getReadyItems,
    getProcessingItems,
    
    // 行为代理
    cancelItem: (id: string) => {
      const item = getMediaItem(id)
      if (item) UnifiedMediaItemActions.cancel(item)
    },
    
    retryItem: (id: string) => {
      const item = getMediaItem(id)
      if (item) UnifiedMediaItemActions.retry(item)
    }
  }
}
```

## 组件层使用

### Vue组件中的使用

```vue
<template>
  <div v-for="item in mediaItems" :key="item.id" class="media-item">
    <!-- ✅ 直接使用响应式属性 -->
    <div class="name">{{ item.name }}</div>
    <div class="status">{{ item.mediaStatus }}</div>
    
    <!-- ✅ 使用查询函数 -->
    <div v-if="UnifiedMediaItemQueries.isProcessing(item)" class="progress">
      进度: {{ UnifiedMediaItemQueries.getProgress(item) || 0 }}%
    </div>
    
    <!-- ✅ 条件渲染自动响应 -->
    <button 
      v-if="UnifiedMediaItemQueries.isReady(item)"
      @click="handleUse(item)"
    >
      使用媒体
    </button>
    
    <button 
      v-if="UnifiedMediaItemQueries.canRetry(item)"
      @click="handleRetry(item)"
    >
      重试
    </button>
  </div>
</template>

<script setup lang="ts">
import { UnifiedMediaItemQueries, UnifiedMediaItemActions } from '@/Unified/MediaItem'
import { useUnifiedStore } from '@/Unified/stores/UnifiedStore'

const unifiedStore = useUnifiedStore()

// ✅ 响应式计算属性自动工作
const mediaItems = computed(() => unifiedStore.mediaModule.mediaItems)

const readyItems = computed(() => 
  mediaItems.value.filter(UnifiedMediaItemQueries.isReady)
)

function handleUse(item: UnifiedMediaItemData) {
  if (UnifiedMediaItemQueries.isReady(item)) {
    // 使用媒体项目
  }
}

function handleRetry(item: UnifiedMediaItemData) {
  UnifiedMediaItemActions.retry(item)
  // ✅ UI会自动更新，无需手动触发
}
</script>
```

## 迁移策略

### 阶段1：创建新架构（并行运行）
1. 创建 `UnifiedMediaItemData` 接口和相关函数
2. 创建新的Store模块
3. 在新组件中使用新架构

### 阶段2：渐进式迁移
1. 逐个组件从类模式迁移到数据+函数模式
2. 保持API兼容性，提供适配器
3. 更新测试用例

### 阶段3：清理旧代码
1. 移除 `UnifiedMediaItem` 类
2. 清理相关的回调机制
3. 优化性能和类型定义

## 总结

通过"核心数据 + 行为分离"的重构方案，我们可以：
1. **彻底解决**Vue3响应式问题
2. **提升**代码质量和可维护性
3. **保持**现有业务逻辑不变
4. **获得**更好的开发体验


---

# 数据源重构方案

## 数据源的响应式问题

与 `UnifiedMediaItem` 类似，当前的 `BaseDataSource` 及其子类也存在相同的响应式问题：

```typescript
// 当前问题
class BaseDataSource {
  protected status: DataSourceStatus = 'pending'  // ❌ 修改不触发响应式
  protected progress: number = 0                   // ❌ 修改不触发响应式

  setAcquiring(): void {
    this.status = 'acquiring'    // ❌ Vue3无法检测
    this.progress = 0
    this.notifyUpdate()          // ❌ 需要手动回调
  }
}
```

## 数据源重构设计

### 1. 核心数据结构

#### 基础数据源接口
```typescript
// 基础数据源数据接口
interface BaseDataSourceData {
  readonly id: string
  readonly type: string
  status: DataSourceStatus
  progress: number
  errorMessage?: string
  taskId?: string
  file: File | null
  url: string | null
}

// 用户选择文件数据源
interface UserSelectedFileSourceData extends BaseDataSourceData {
  type: 'user-selected'
  selectedFile: File
}

// 远程文件数据源
interface RemoteFileSourceData extends BaseDataSourceData {
  type: 'remote'
  remoteUrl: string
  config: RemoteFileConfig
  downloadedBytes: number
  totalBytes: number
  downloadSpeed?: string
  startTime?: number
}

// 联合类型
type UnifiedDataSourceData =
  | UserSelectedFileSourceData
  | RemoteFileSourceData
```

#### 工厂函数
```typescript
export const DataSourceFactory = {
  createUserSelectedSource(file: File): UserSelectedFileSourceData {
    return reactive({
      id: generateUUID4(),
      type: 'user-selected',
      status: 'pending',
      progress: 0,
      file: null,
      url: null,
      selectedFile: file
    })
  },

  createRemoteSource(
    remoteUrl: string,
    config: RemoteFileConfig = {}
  ): RemoteFileSourceData {
    return reactive({
      id: generateUUID4(),
      type: 'remote',
      status: 'pending',
      progress: 0,
      file: null,
      url: null,
      remoteUrl,
      config,
      downloadedBytes: 0,
      totalBytes: 0
    })
  }
}
```

### 2. 行为函数模块

#### 通用数据源行为
```typescript
export const UnifiedDataSourceActions = {
  // 开始获取
  startAcquisition(source: UnifiedDataSourceData): void {
    if (source.status !== 'pending') return

    source.taskId = generateUUID4()
    source.status = 'acquiring'
    source.progress = 0
    source.errorMessage = undefined

    // 根据类型分发到具体处理器
    switch (source.type) {
      case 'user-selected':
        UserSelectedFileActions.executeAcquisition(source)
        break
      case 'remote':
        RemoteFileActions.executeAcquisition(source)
        break
    }
  },

  // 更新进度
  updateProgress(source: UnifiedDataSourceData, progress: number): void {
    if (source.status !== 'acquiring') return
    source.progress = Math.max(0, Math.min(100, progress))
  },

  // 设置完成
  setAcquired(source: UnifiedDataSourceData, file: File, url: string): void {
    source.file = file
    source.url = url
    source.status = 'acquired'
    source.progress = 100
  },

  // 设置错误
  setError(source: UnifiedDataSourceData, message: string): void {
    source.status = 'error'
    source.errorMessage = message
  },

  // 取消获取
  cancel(source: UnifiedDataSourceData): void {
    if (source.status === 'acquiring') {
      source.status = 'cancelled'
    }
  },

  // 重试
  retry(source: UnifiedDataSourceData): void {
    if (source.status === 'error' || source.status === 'cancelled') {
      source.status = 'pending'
      source.progress = 0
      source.errorMessage = undefined
      source.taskId = undefined
    }
  }
}
```

#### 特定类型行为
```typescript
// 用户选择文件特定行为
export const UserSelectedFileActions = {
  executeAcquisition(source: UserSelectedFileSourceData): void {
    // 验证文件有效性
    if (!this.validateFile(source.selectedFile)) {
      DataSourceActions.setError(source, '文件格式不支持')
      return
    }

    // 创建URL
    const url = URL.createObjectURL(source.selectedFile)
    DataSourceActions.setAcquired(source, source.selectedFile, url)
  },

  validateFile(file: File): boolean {
    // 文件验证逻辑
    const supportedTypes = [
      'video/mp4', 'video/webm', 'video/ogg',
      'audio/mp3', 'audio/wav', 'audio/ogg',
      'image/jpeg', 'image/png', 'image/gif'
    ]
    return supportedTypes.includes(file.type)
  }
}

// 远程文件特定行为
export const RemoteFileActions = {
  async executeAcquisition(source: RemoteFileSourceData): Promise<void> {
    try {
      source.startTime = Date.now()

      const response = await fetch(source.remoteUrl, {
        headers: source.config.headers,
        signal: this.createAbortSignal(source.config.timeout)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await this.downloadWithProgress(response, source)
      const fileName = this.extractFileName(source.remoteUrl)
      const file = new File([blob], fileName)
      const url = URL.createObjectURL(file)

      DataSourceActions.setAcquired(source, file, url)
    } catch (error) {
      DataSourceActions.setError(source, error.message)
    }
  },

  async downloadWithProgress(
    response: Response,
    source: RemoteFileSourceData
  ): Promise<Blob> {
    const contentLength = response.headers.get('content-length')
    source.totalBytes = contentLength ? parseInt(contentLength) : 0

    const reader = response.body?.getReader()
    const chunks: Uint8Array[] = []

    while (true) {
      const { done, value } = await reader!.read()
      if (done) break

      chunks.push(value)
      source.downloadedBytes += value.length

      // 计算下载速度
      if (source.startTime) {
        const elapsed = (Date.now() - source.startTime) / 1000
        const speed = source.downloadedBytes / elapsed
        source.downloadSpeed = this.formatSpeed(speed)
      }

      // 更新进度
      if (source.totalBytes > 0) {
        const progress = (source.downloadedBytes / source.totalBytes) * 100
        DataSourceActions.updateProgress(source, progress)
      }
    }

    return new Blob(chunks)
  },

  formatSpeed(bytesPerSecond: number): string {
    if (bytesPerSecond < 1024) return `${bytesPerSecond.toFixed(0)} B/s`
    if (bytesPerSecond < 1024 * 1024) return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`
    return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`
  },

  extractFileName(url: string): string {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname
      return pathname.split('/').pop() || 'download'
    } catch {
      return 'download'
    }
  },

  createAbortSignal(timeout?: number): AbortSignal | undefined {
    if (!timeout) return undefined
    const controller = new AbortController()
    setTimeout(() => controller.abort(), timeout)
    return controller.signal
  }
}
```

### 3. 查询函数模块

```typescript
export const DataSourceQueries = {
  // 状态查询
  isPending(source: UnifiedDataSourceData): boolean {
    return source.status === 'pending'
  },

  isAcquiring(source: UnifiedDataSourceData): boolean {
    return source.status === 'acquiring'
  },

  isAcquired(source: UnifiedDataSourceData): boolean {
    return source.status === 'acquired'
  },

  hasError(source: UnifiedDataSourceData): boolean {
    return source.status === 'error'
  },

  isCancelled(source: UnifiedDataSourceData): boolean {
    return source.status === 'cancelled'
  },

  // 类型守卫
  isUserSelectedSource(source: UnifiedDataSourceData): source is UserSelectedFileSourceData {
    return source.type === 'user-selected'
  },

  isRemoteSource(source: UnifiedDataSourceData): source is RemoteFileSourceData {
    return source.type === 'remote'
  },

  // 通用属性访问
  getType(source: UnifiedDataSourceData): string {
    return source.type
  },

  getStatus(source: UnifiedDataSourceData): DataSourceStatus {
    return source.status
  },

  getProgress(source: UnifiedDataSourceData): number {
    return source.progress
  },

  getError(source: UnifiedDataSourceData): string | undefined {
    return source.errorMessage
  },

  getTaskId(source: UnifiedDataSourceData): string | undefined {
    return source.taskId
  },

  getFile(source: UnifiedDataSourceData): File | null {
    return source.file
  },

  getUrl(source: UnifiedDataSourceData): string | null {
    return source.url
  },

  // 特定类型查询
  getSelectedFile(source: UnifiedDataSourceData): File | null {
    return DataSourceQueries.isUserSelectedSource(source) ? source.selectedFile : null
  },

  getRemoteUrl(source: UnifiedDataSourceData): string | null {
    return DataSourceQueries.isRemoteSource(source) ? source.remoteUrl : null
  },

  getDownloadStats(source: UnifiedDataSourceData): DownloadStats | null {
    if (!DataSourceQueries.isRemoteSource(source)) return null

    return {
      downloadedBytes: source.downloadedBytes,
      totalBytes: source.totalBytes,
      downloadSpeed: source.downloadSpeed,
      startTime: source.startTime
    }
  },

  // 业务查询
  canRetry(source: UnifiedDataSourceData): boolean {
    return source.status === 'error' || source.status === 'cancelled'
  },

  canCancel(source: UnifiedDataSourceData): boolean {
    return source.status === 'acquiring'
  }
}
```

## 管理器适配

### 重要说明：管理器不需要重构

数据源管理器（`BaseDataSourceManager`、`UserSelectedFileManager` 等）**不需要重构**，因为：

1. **管理器本身就是行为层**：负责流程控制，不存储业务数据
2. **职责清晰**：任务队列、并发控制、超时处理等逻辑与数据结构无关
3. **已经解耦**：管理器通过接口与数据源交互

### 管理器适配方案

管理器只需要更新调用方式，从调用实例方法改为调用静态函数：

```typescript
// 管理器内部实现的小幅调整
class UserSelectedFileManager extends BaseDataSourceManager {
  protected async processAcquisition(
    source: UserSelectedFileSourceData,
    taskId: string
  ): Promise<void> {
    // 原来：source.setAcquiring()
    // 现在：DataSourceActions.setAcquiring(source)
    DataSourceActions.setAcquiring(source)

    try {
      // 原来：source.executeAcquisition()
      // 现在：UserSelectedFileActions.executeAcquisition(source)
      UserSelectedFileActions.executeAcquisition(source)
    } catch (error) {
      // 原来：source.setError(error.message)
      // 现在：DataSourceActions.setError(source, error.message)
      DataSourceActions.setError(source, error.message)
    }
  }
}
```

### 保持不变的部分

- ✅ **管理器注册系统**：`registerManager()`, `getManager()` 等
- ✅ **任务队列管理**：队列处理、并发控制逻辑
- ✅ **管理器公共接口**：`startAcquisition()`, `cancelAcquisition()` 等
- ✅ **统计和监控**：任务统计、性能监控等功能

## 与MediaItem的集成

### 更新MediaItem数据结构

```typescript
// MediaItem使用新的数据源结构
interface UnifiedMediaItemData {
  id: string
  name: string
  mediaStatus: MediaStatus
  source: UnifiedDataSourceData  // ✅ 使用新的响应式数据源
  webav?: WebAVObjects
  duration?: number
  createdAt: string
}
```

### 更新MediaItem行为

```typescript
export const UnifiedMediaItemActions = {
  // 处理数据源状态变化
  handleSourceStatusChange(
    item: UnifiedMediaItemData,
    source: UnifiedDataSourceData
  ): void {
    const sourceStatus = DataSourceQueries.getStatus(source)

    switch (sourceStatus) {
      case 'acquiring':
        UnifiedMediaItemActions.transitionTo(item, 'asyncprocessing')
        break
      case 'acquired':
        UnifiedMediaItemActions.transitionTo(item, 'webavdecoding')
        UnifiedMediaItemActions.startWebAVProcessing(item)
        break
      case 'error':
        UnifiedMediaItemActions.transitionTo(item, 'error', {
          type: 'error',
          errorMessage: DataSourceQueries.getError(source) || '数据源获取失败',
          errorCode: 'SOURCE_ERROR'
        })
        break
      case 'cancelled':
        UnifiedMediaItemActions.transitionTo(item, 'cancelled')
        break
    }
  },

  // 获取文件对象（更新实现）
  getFile(item: UnifiedMediaItemData): File | undefined {
    return DataSourceQueries.getFile(item.source) || undefined
  },

  // 取消媒体项目（更新实现）
  cancel(item: UnifiedMediaItemData): void {
    if (UnifiedMediaItemQueries.isProcessing(item)) {
      // 取消数据源
      if (DataSourceQueries.canCancel(item.source)) {
        DataSourceActions.cancel(item.source)
      }
      UnifiedMediaItemActions.transitionTo(item, 'cancelled')
    }
  }
}
```

## 数据源重构的优势

### 1. 统一的架构模式
- MediaItem和DataSource都采用"数据+行为分离"
- 整个系统架构一致，降低认知负担

### 2. 完美的响应式支持
- 所有数据源状态变化自动触发UI更新
- 进度条、状态显示等无需手动刷新

### 3. 更好的类型安全
- 联合类型 + 类型守卫提供编译时检查
- 避免运行时类型错误

### 4. 简化的扩展机制
添加新数据源类型只需要：
- 定义新的数据接口
- 实现对应的行为函数
- 更新联合类型定义

### 5. 保持管理器稳定
- 任务管理、队列控制等核心逻辑无需改动
- 降低重构风险和工作量

## 数据源迁移策略

### 阶段1：创建新架构（1周）
1. 定义数据源数据接口
2. 实现行为函数和查询函数
3. 创建工厂函数
4. 编写单元测试

### 阶段2：适配管理器（3天）
1. 更新管理器调用方式
2. 保持公共接口不变
3. 测试管理器功能

### 阶段3：集成MediaItem（3天）
1. 更新MediaItem使用新数据源
2. 更新相关的行为函数
3. 测试端到端流程

### 阶段4：组件层迁移（1周）
1. 更新组件使用新的查询函数
2. 验证响应式更新正常
3. 清理旧的回调机制

### 阶段5：清理和优化（2天）
1. 移除旧的类定义
2. 清理未使用的代码
3. 性能优化和文档更新

## 数据源重构总结

通过将数据源重构为"核心数据 + 行为分离"模式：

1. **解决响应式问题**：所有状态变化自动触发UI更新
2. **保持架构一致**：与MediaItem采用相同的设计模式
3. **最小化重构范围**：管理器等核心逻辑无需改动
4. **提升开发体验**：更好的类型安全和调试体验
5. **简化未来扩展**：新数据源类型更容易添加

这个重构方案既解决了当前的响应式问题，又为系统的长期发展提供了坚实的架构基础。

---

# 完整重构计划

## 总体时间安排

### MediaItem重构（2-3周）
- 阶段1：创建新架构（1周）
- 阶段2：渐进式迁移（1周）
- 阶段3：清理旧代码（3-5天）

### 数据源重构（2周）
- 阶段1：创建新架构（1周）
- 阶段2-5：适配和迁移（1周）

### 总计：4-5周完成整体重构

## 重构优先级

1. **高优先级**：MediaItem核心重构
   - 解决最主要的响应式问题
   - 为后续重构奠定基础

2. **中优先级**：数据源重构
   - 保持架构一致性
   - 完善响应式支持

3. **低优先级**：性能优化和文档完善
   - 代码清理和优化
   - 开发文档更新

## 预期收益总结

通过完整的"核心数据 + 行为分离"重构：

1. **彻底解决Vue3响应式问题**
2. **建立统一的架构模式**
3. **提升代码质量和可维护性**
4. **改善开发体验和调试效率**
5. **为未来功能扩展奠定基础**

这是一个具有战略意义的重构项目，将显著提升项目的技术水平和长期竞争力。
