# 数据源基础管理器类型设计（响应式重构版）

## 概述

数据源管理器负责具体的文件获取逻辑，采用单例模式进行任务调度和并发控制。本文档基于"核心数据与行为分离"的重构方案，定义了与响应式数据源配合的管理器设计。

## 重构说明

### 管理器不需要重构的原因
数据源管理器（`BaseDataSourceManager`、`UserSelectedFileManager` 等）**不需要重构**，因为：

1. **管理器本身就是行为层**：负责流程控制，不存储业务数据
2. **职责清晰**：任务队列、并发控制、超时处理等逻辑与数据结构无关
3. **已经解耦**：管理器通过接口与数据源交互

### 适配方案
管理器只需要更新调用方式，从调用实例方法改为调用静态函数。

## 设计理念

### 1. 职责分离
- **数据源数据**：纯响应式状态对象，存储所有状态信息
- **数据源行为函数**：无状态函数，处理数据源操作逻辑
- **数据源管理器**：专注任务调度、并发控制、资源管理
- **媒体实例**：处理业务逻辑、状态转换

### 2. 单例模式
每种数据源类型对应一个管理器实例，统一管理该类型的所有获取任务。

### 3. 异步任务管理
支持任务队列、并发控制、取消操作和重试机制。

## 基础管理器抽象类（适配版）

```typescript
/**
 * 数据源管理器基础抽象类 - 适配响应式数据源
 */
abstract class DataSourceManager<T extends UnifiedDataSourceData> {
  protected tasks: Map<string, AcquisitionTask<T>> = new Map()
  protected maxConcurrentTasks: number = 5
  protected currentRunningTasks: number = 0
  protected taskQueue: string[] = []

  // ==================== 公共接口 ====================

  /**
   * 开始获取任务
   */
  startAcquisition(source: T, taskId: string): void {
    const task: AcquisitionTask<T> = {
      id: taskId,
      source,
      status: 'pending',
      createdAt: Date.now(),
      retryCount: 0
    }

    this.tasks.set(taskId, task)
    this.taskQueue.push(taskId)
    this.processQueue()
  }

  /**
   * 取消获取任务
   */
  cancelAcquisition(taskId: string): void {
    const task = this.tasks.get(taskId)
    if (!task) return

    task.status = 'cancelled'

    if (task.abortController) {
      task.abortController.abort()
    }

    // ✅ 使用行为函数替代实例方法
    UnifiedDataSourceActions.cancel(task.source)
    this.cleanupTask(taskId)
  }

  /**
   * 重试获取任务
   */
  retryAcquisition(taskId: string): void {
    const task = this.tasks.get(taskId)
    if (!task) return

    task.status = 'pending'
    task.retryCount++
    task.abortController = undefined

    this.taskQueue.push(taskId)
    this.processQueue()
  }

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): AcquisitionTaskStatus | undefined {
    return this.tasks.get(taskId)?.status
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): AcquisitionTask<T>[] {
    return Array.from(this.tasks.values())
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompletedTasks(): void {
    const completedTasks = Array.from(this.tasks.entries())
      .filter(([_, task]) => 
        task.status === 'completed' || 
        task.status === 'failed' || 
        task.status === 'cancelled'
      )

    completedTasks.forEach(([taskId]) => {
      this.tasks.delete(taskId)
    })
  }

  // ==================== 抽象方法 ====================
  
  /**
   * 执行具体的获取逻辑
   */
  protected abstract executeTask(task: AcquisitionTask<T>): Promise<void>

  /**
   * 获取管理器类型名称
   */
  abstract getManagerType(): string

  // ==================== 内部方法 ====================
  
  /**
   * 处理任务队列
   */
  protected processQueue(): void {
    while (
      this.currentRunningTasks < this.maxConcurrentTasks && 
      this.taskQueue.length > 0
    ) {
      const taskId = this.taskQueue.shift()!
      const task = this.tasks.get(taskId)
      
      if (task && task.status === 'pending') {
        this.runTask(task)
      }
    }
  }

  /**
   * 运行单个任务
   */
  protected async runTask(task: AcquisitionTask<T>): Promise<void> {
    task.status = 'running'
    task.startedAt = Date.now()
    task.abortController = new AbortController()

    this.currentRunningTasks++

    // ✅ 使用行为函数替代实例方法
    UnifiedDataSourceActions.updateProgress(task.source, 0)
    task.source.status = 'acquiring'

    try {
      await this.executeTask(task)
      task.status = 'completed'
      task.completedAt = Date.now()
    } catch (error) {
      task.status = 'failed'
      task.error = error instanceof Error ? error.message : String(error)

      // ✅ 使用行为函数设置错误
      UnifiedDataSourceActions.setError(task.source, task.error)

      // 检查是否需要重试
      if (this.shouldRetry(task)) {
        setTimeout(() => {
          this.retryAcquisition(task.id)
        }, this.getRetryDelay(task.retryCount))
      }
    } finally {
      this.currentRunningTasks--
      this.processQueue() // 处理队列中的下一个任务
    }
  }

  /**
   * 判断是否应该重试
   */
  protected shouldRetry(task: AcquisitionTask<T>): boolean {
    const maxRetries = this.getMaxRetries(task.source)
    return task.retryCount < maxRetries && task.status === 'failed'
  }

  /**
   * 获取最大重试次数
   */
  protected getMaxRetries(source: T): number {
    // 子类可以重写此方法
    return 3
  }

  /**
   * 获取重试延迟时间
   */
  protected getRetryDelay(retryCount: number): number {
    // 指数退避策略
    return Math.min(1000 * Math.pow(2, retryCount), 30000)
  }

  /**
   * 清理任务资源
   */
  protected cleanupTask(taskId: string): void {
    const task = this.tasks.get(taskId)
    if (task?.abortController) {
      task.abortController.abort()
    }
    
    // 可以在子类中重写以添加特定的清理逻辑
  }

  /**
   * 设置最大并发任务数
   */
  setMaxConcurrentTasks(max: number): void {
    this.maxConcurrentTasks = Math.max(1, max)
  }

  /**
   * 获取统计信息
   */
  getStats(): ManagerStats {
    const tasks = Array.from(this.tasks.values())
    
    return {
      totalTasks: tasks.length,
      pendingTasks: tasks.filter(t => t.status === 'pending').length,
      runningTasks: tasks.filter(t => t.status === 'running').length,
      completedTasks: tasks.filter(t => t.status === 'completed').length,
      failedTasks: tasks.filter(t => t.status === 'failed').length,
      cancelledTasks: tasks.filter(t => t.status === 'cancelled').length,
      currentRunningTasks: this.currentRunningTasks,
      maxConcurrentTasks: this.maxConcurrentTasks
    }
  }
}
```

## 类型定义

```typescript
/**
 * 获取任务接口 - 适配响应式数据源
 */
interface AcquisitionTask<T extends UnifiedDataSourceData> {
  id: string
  source: T
  status: AcquisitionTaskStatus
  createdAt: number
  startedAt?: number
  completedAt?: number
  retryCount: number
  error?: string
  abortController?: AbortController
}

/**
 * 任务状态类型
 */
type AcquisitionTaskStatus =
  | 'pending'     // 等待执行
  | 'running'     // 执行中
  | 'completed'   // 已完成
  | 'failed'      // 失败
  | 'cancelled'   // 已取消

/**
 * 管理器统计信息
 */
interface ManagerStats {
  totalTasks: number
  pendingTasks: number
  runningTasks: number
  completedTasks: number
  failedTasks: number
  cancelledTasks: number
  currentRunningTasks: number
  maxConcurrentTasks: number
}

/**
 * 管理器配置接口
 */
interface ManagerConfig {
  maxConcurrentTasks?: number
  maxRetries?: number
  retryDelay?: number
  timeout?: number
}
```

## 管理器注册系统

```typescript
/**
 * 管理器注册中心
 */
class DataSourceManagerRegistry {
  private static instance: DataSourceManagerRegistry
  private managers: Map<string, DataSourceManager<any>> = new Map()

  static getInstance(): DataSourceManagerRegistry {
    if (!this.instance) {
      this.instance = new DataSourceManagerRegistry()
    }
    return this.instance
  }

  /**
   * 注册管理器
   */
  register<T extends BaseDataSource>(
    type: string, 
    manager: DataSourceManager<T>
  ): void {
    this.managers.set(type, manager)
  }

  /**
   * 获取管理器
   */
  getManager<T extends BaseDataSource>(type: string): DataSourceManager<T> | undefined {
    return this.managers.get(type)
  }

  /**
   * 获取所有管理器
   */
  getAllManagers(): Map<string, DataSourceManager<any>> {
    return new Map(this.managers)
  }

  /**
   * 获取全局统计信息
   */
  getGlobalStats(): GlobalManagerStats {
    const stats: GlobalManagerStats = {
      totalManagers: this.managers.size,
      managerStats: new Map()
    }

    this.managers.forEach((manager, type) => {
      stats.managerStats.set(type, manager.getStats())
    })

    return stats
  }

  /**
   * 清理所有管理器的已完成任务
   */
  cleanupAllCompletedTasks(): void {
    this.managers.forEach(manager => {
      manager.cleanupCompletedTasks()
    })
  }
}

/**
 * 全局管理器统计信息
 */
interface GlobalManagerStats {
  totalManagers: number
  managerStats: Map<string, ManagerStats>
}
```

## 工具函数

```typescript
/**
 * 生成任务ID
 */
function generateTaskId(): string {
  return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 创建AbortController（兼容性处理）
 */
function createAbortController(): AbortController {
  if (typeof AbortController !== 'undefined') {
    return new AbortController()
  }
  
  // 简单的polyfill
  return {
    signal: { aborted: false },
    abort: function() { this.signal.aborted = true }
  } as AbortController
}

/**
 * 延迟执行
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 带超时的Promise
 */
function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => 
      setTimeout(() => reject(new Error('操作超时')), timeoutMs)
    )
  ])
}
```

## 管理器适配示例

### 用户选择文件管理器适配

```typescript
/**
 * 用户选择文件管理器 - 适配响应式数据源
 */
class UserSelectedFileManager extends DataSourceManager<UserSelectedFileSourceData> {
  private static instance: UserSelectedFileManager

  static getInstance(): UserSelectedFileManager {
    if (!this.instance) {
      this.instance = new UserSelectedFileManager()
    }
    return this.instance
  }

  protected async executeTask(task: AcquisitionTask<UserSelectedFileSourceData>): Promise<void> {
    // ✅ 使用行为函数替代实例方法
    UserSelectedFileActions.executeAcquisition(task.source)
  }

  getManagerType(): string {
    return 'user-selected'
  }
}
```

### 远程文件管理器适配

```typescript
/**
 * 远程文件管理器 - 适配响应式数据源
 */
class RemoteFileManager extends DataSourceManager<RemoteFileSourceData> {
  private static instance: RemoteFileManager

  static getInstance(): RemoteFileManager {
    if (!this.instance) {
      this.instance = new RemoteFileManager()
    }
    return this.instance
  }

  protected async executeTask(task: AcquisitionTask<RemoteFileSourceData>): Promise<void> {
    // ✅ 使用行为函数替代实例方法
    await RemoteFileActions.executeAcquisition(task.source)
  }

  getManagerType(): string {
    return 'remote'
  }

  protected getMaxRetries(source: RemoteFileSourceData): number {
    return source.config.retryCount || 3
  }
}
```

## 设计优势

### 1. 最小化重构范围
- 管理器核心逻辑无需改动
- 只需要更新调用方式
- 保持现有的任务管理机制

### 2. 统一的任务管理
- 所有数据源类型都使用相同的任务管理机制
- 支持队列、并发控制、取消和重试
- 提供详细的任务状态和统计信息

### 3. 可配置的并发控制
- 支持设置最大并发任务数
- 自动队列管理，避免资源过度消耗
- 支持动态调整并发参数

### 4. 完善的错误处理
- 支持自动重试机制
- 指数退避策略避免频繁重试
- 详细的错误信息记录

### 5. 扩展性强
- 抽象类定义清晰的扩展点
- 支持插件化的管理器注册
- 易于添加新的数据源类型

### 6. 资源管理
- 自动清理已完成的任务
- 支持任务取消和资源释放
- 内存使用优化

### 7. 响应式兼容
- 与响应式数据源完美配合
- 状态变化自动触发UI更新
- 保持管理器的稳定性
