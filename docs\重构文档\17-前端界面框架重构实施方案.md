# 前端界面框架重构实施方案

## 📋 方案概述

基于统一异步源架构，采用**渐进式重构策略**，完全使用 `frontend/` 项目的前端样式，但底层结构全部改用统一类型。

### 🎯 核心策略
- **界面样式优先**：先搭起完整的界面框架，不管任何功能
- **空状态启动**：一开始界面就是空的，无任何内容
- **统一类型底层**：底层完全基于统一异步源架构
- **渐进式迁移**：框架搭建完成后，逐步移植原项目代码逻辑

## 🏗️ 阶段1：界面框架搭建（空状态）

### 1. 直接复制的内容

#### ✅ 完整复制（包括逻辑）
```
frontend/src/styles/ → refact-frontend/src/styles/
frontend/src/router/ → refact-frontend/src/router/

简单UI组件（完整复制）：
- HoverButton.vue
- NumberInput.vue  
- SliderInput.vue
- LoadingOverlay.vue
- NotificationContainer.vue
- 所有 icons/ 目录下的图标组件
```

#### 🔄 只复制模板+样式，移除逻辑
```
复杂组件处理：
- MediaLibrary.vue → UnifiedMediaLibrary.vue（保留布局，移除媒体加载逻辑）
- Timeline.vue → UnifiedTimeline.vue（保留时间轴UI，移除轨道内容渲染）
- PreviewWindow.vue → UnifiedPreviewWindow.vue（保留窗口样式，移除视频播放逻辑）
- PlaybackControls.vue → UnifiedPlaybackControls.vue（保留按钮样式，移除播放控制逻辑）
- PropertiesPanel.vue → UnifiedPropertiesPanel.vue（保留面板布局，移除属性绑定逻辑）
```

### 2. 命名策略：Unified前缀

#### ✅ 新组件命名规范
```
原组件 → 新组件（Unified前缀）
MediaLibrary.vue → UnifiedMediaLibrary.vue
Timeline.vue → UnifiedTimeline.vue  
PreviewWindow.vue → UnifiedPreviewWindow.vue
PlaybackControls.vue → UnifiedPlaybackControls.vue
PropertiesPanel.vue → UnifiedPropertiesPanel.vue
WebAVRenderer.vue → UnifiedWebAVRenderer.vue
```

**优势**：
- **清晰区分**：一眼就能看出哪些是新架构组件
- **并行开发**：可以保留原组件作为参考，不影响开发
- **渐进替换**：后续可以逐步替换路由中的组件引用

### 3. VideoEditor.vue 空壳搭建

基于原版的布局结构，但各区域显示空状态：

```vue
<!-- 大致结构示意 -->
<template>
  <div class="video-editor">
    <!-- 顶部工具栏 - 保留样式，按钮暂时禁用 -->
    <div class="toolbar">
      <!-- 复制原有按钮布局，但移除点击事件 -->
    </div>
    
    <!-- 主要工作区 -->
    <div class="main-workspace">
      <!-- 左侧媒体库 - 空状态 -->
      <UnifiedMediaLibrary />
      
      <!-- 中间预览区 - 黑屏 -->
      <UnifiedPreviewWindow />
      
      <!-- 右侧属性面板 - 空状态 -->
      <UnifiedPropertiesPanel />
    </div>
    
    <!-- 底部时间轴 - 空轨道 -->
    <div class="timeline-area">
      <UnifiedTimeline />
      <UnifiedPlaybackControls />
    </div>
  </div>
</template>
```

### 4. 空状态设计
- **媒体库**：显示拖拽提示区域，"拖拽文件到此处添加媒体"
- **时间轴**：显示时间刻度和轨道标签，但轨道内容为空
- **预览窗口**：纯黑背景，中央显示播放按钮（灰色不可点击）
- **属性面板**：显示"请选择时间轴上的对象以查看属性"

## 🎬 WebAV组件迁移策略

### 核心原则：直接照搬原项目的成熟方案

#### ✅ 完整复制WebAV生态
```
直接复制（保持原有逻辑）：
- WebAVRenderer.vue → UnifiedWebAVRenderer.vue
- VideoPreviewEngine.vue → UnifiedVideoPreviewEngine.vue
- 所有 *VisibleSprite.ts 工具类
- webavAnimationManager.ts
- webavDebug.ts
- WebAV相关的所有依赖和配置
```

#### 🔄 重命名但保持逻辑不变
- 只是加上 `Unified` 前缀
- 内部的WebAV初始化、错误处理、生命周期管理完全保持原样
- 原项目怎么处理WebAV失败，新项目就怎么处理

#### ✅ WebAV工具类完整迁移
```
utils/ 下的WebAV相关文件直接复制：
- BaseVisibleSprite.ts
- VideoVisibleSprite.ts
- AudioVisibleSprite.ts
- ImageVisibleSprite.ts
- TextVisibleSprite.ts
```

### 空壳状态下的WebAV

#### ✅ WebAV正常初始化
- 渲染器正常启动
- 预览引擎正常创建
- 所有WebAV上下文准备就绪

#### ✅ 但没有媒体内容
- 不加载任何视频/音频文件
- 不创建任何时间轴项目的Sprite
- 预览窗口显示空的渲染场景（黑屏）

#### ✅ 所有接口都可用
- WebAV的所有API都已准备好
- 后续添加媒体时可以直接调用
- 不需要重新初始化任何WebAV组件

## 🗂️ 状态管理：激进重构策略

### ❌ 完全抛弃原Store
```
完全移除：
- frontend/src/stores/videoStore.ts
- 所有相关的旧状态管理逻辑
```

### ✅ 全新建立统一Store
```
refact-frontend/src/stores/unified/
├── unifiedProjectStore.ts    # 项目管理（替代原项目管理逻辑）
├── unifiedMediaStore.ts      # 媒体管理（基于UnifiedMediaItem）
├── unifiedTimelineStore.ts   # 时间轴管理（基于UnifiedTimelineItem）
├── unifiedPlaybackStore.ts   # 播放控制（统一播放状态）
└── index.ts                  # 统一导出和初始化
```

## 🏗️ 阶段2：统一类型基础架构

### 1. 类型定义层
```
refact-frontend/src/types/unified/
├── DataSource.ts        # 数据源类型定义
├── MediaItem.ts         # 统一媒体项目类型
├── TimelineItem.ts      # 统一时间轴项目类型
├── Command.ts           # 操作记录系统类型
└── index.ts            # 统一导出
```

### 2. 工具函数层
```
refact-frontend/src/utils/unified/
├── DataSourceManager.ts    # 数据源管理器
├── MediaItemFactory.ts     # 媒体项目工厂
├── StateTransition.ts      # 状态转换管理
└── index.ts               # 统一导出
```

## 🔄 阶段3：功能逐步迁移策略

### 迁移优先级排序
1. **项目管理功能**（最简单）
   - 项目创建、保存、加载
   - 项目列表展示

2. **媒体库功能**（中等复杂）
   - 文件拖拽添加
   - 媒体项目展示
   - 基于统一类型的状态显示

3. **时间轴基础功能**（复杂）
   - 媒体拖拽到时间轴
   - 基础的时间轴项目显示
   - 简单的编辑操作

4. **播放和预览功能**（最复杂）
   - 预览窗口渲染
   - 播放控制逻辑
   - 实时预览更新

## 📋 实施要点

### ✅ 关键原则
- **样式完全一致**：视觉效果与原项目完全相同
- **功能完全空白**：初期不实现任何业务逻辑
- **架构完全统一**：底层完全基于统一异步源
- **WebAV完全复用**：直接使用原项目的WebAV集成方案

### ✅ 成功标准
- 界面布局与原项目完全一致
- 所有UI组件正常渲染
- WebAV组件正确初始化
- 路由导航正常工作
- 空状态显示正确

---

*文档创建时间：2025-01-20*
*基于统一异步源架构的前端界面框架重构实施方案*
