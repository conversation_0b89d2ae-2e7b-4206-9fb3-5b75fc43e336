# 统一时间轴项目设计 - 使用示例（响应式重构版）

## 概述

基于"核心数据 + 行为分离"的重构方案，统一时间轴项目采用与数据源、媒体项目一致的响应式架构模式，**完全移除复杂的上下文模板系统**，改为直接基于关联媒体项目状态计算UI显示信息。

## 重构背景

### 架构一致性问题
当前统一时间轴项目使用类模式，与已重构的数据源、媒体项目的"核心数据 + 行为分离"模式不一致：
- 数据源和媒体项目：已采用响应式数据对象 + 无状态函数
- 时间轴项目：仍使用类实例，存在响应式支持问题

### 解决方案
采用统一的"核心数据 + 行为分离"模式：
- **核心数据**：纯粹的响应式状态对象，使用 `reactive()` 包装
- **行为函数**：无状态的纯函数，操作数据对象
- **状态显示**：直接基于关联媒体项目状态计算，无需复杂上下文模板

## 响应式数据结构设计

### 1. 统一时间轴项目数据接口

```typescript
import { reactive } from 'vue'
import type { MediaType } from '@/types'

/**
 * 统一时间轴项目数据接口 - 纯响应式状态对象
 *
 * 设计理念：
 * - 移除复杂的 statusContext，状态显示直接基于关联媒体项目计算
 * - 保持数据结构简洁，专注于时间轴项目的核心属性
 * - 通过 mediaItemId 关联媒体项目，实现状态同步
 */
export interface UnifiedTimelineItemData {
  // ==================== 核心属性 ====================
  readonly id: string
  mediaItemId: string // 关联的统一媒体项目ID
  trackId?: string

  // ==================== 状态管理 ====================
  timelineStatus: TimelineItemStatus // 仅3状态：ready|loading|error

  // ==================== 媒体信息 ====================
  mediaType: MediaType | 'unknown' // 从关联的媒体项目同步

  // ==================== 时间范围 ====================
  timeRange: {
    timelineStartTime: number // 时间轴开始时间（帧数）
    timelineEndTime: number   // 时间轴结束时间（帧数）
  }

  // ==================== 基础配置 ====================
  config: BasicTimelineConfig // 静态配置信息

  // ==================== Sprite引用 ====================
  spriteId?: string // Sprite ID，由SpriteLifecycleManager管理
}

/**
 * 时间轴项目状态枚举 - 简化为3个核心状态
 */
export type TimelineItemStatus = 'loading' | 'ready' | 'error'

/**
 * 媒体状态到时间轴状态的映射表
 */
export const MEDIA_TO_TIMELINE_STATUS_MAP = {
  'pending': 'loading',           // 等待开始 → 加载中
  'asyncprocessing': 'loading',   // 异步处理中 → 加载中
  'webavdecoding': 'loading',     // WebAV解析中 → 加载中
  'ready': 'ready',               // 就绪 → 就绪
  'error': 'error',               // 错误 → 错误
  'cancelled': 'error',           // 已取消 → 错误
  'missing': 'error'              // 文件缺失 → 错误
} as const
```

## 状态显示计算示例

### 1. 基于关联媒体项目的状态显示

```typescript
import { TimelineStatusDisplayUtils } from '@/unified/timelineitem'
import { useUnifiedStore } from '@/unified/unifiedStore'

/**
 * 获取时间轴项目的状态显示信息
 * 直接基于关联的媒体项目状态计算，无需复杂的上下文模板
 */
function getTimelineDisplayInfo(timelineData: UnifiedTimelineItemData): StatusDisplayInfo {
  // 1. 获取关联的媒体项目
  const unifiedStore = useUnifiedStore()
  const mediaData = unifiedStore.getMediaItem(timelineData.mediaItemId)

  // 2. 直接基于媒体项目状态计算显示信息
  return TimelineStatusDisplayUtils.getStatusDisplayInfo(mediaData)
}

/**
 * 使用示例：显示时间轴项目状态
 */
function displayTimelineStatus(timelineData: UnifiedTimelineItemData) {
  const displayInfo = getTimelineDisplayInfo(timelineData)

  console.log(`状态文本: ${displayInfo.text}`)

  if (displayInfo.hasProgress) {
    const progressText = displayInfo.speed
      ? `${displayInfo.percent}% (${displayInfo.speed})`
      : `${displayInfo.percent}%`
    console.log(`进度: ${progressText}`)
  }

  if (displayInfo.hasError) {
    console.log(`错误: ${displayInfo.errorMessage}`)
    console.log(`可重试: ${displayInfo.recoverable}`)
  }
}
```

### 2. 响应式行为函数示例

```typescript
import { reactive } from 'vue'
import { generateUUID4 } from '@/utils'

// ==================== 工厂函数 ====================

/**
 * 创建响应式时间轴项目数据对象
 */
export function createTimelineItemData(options: {
  mediaItemId: string
  trackId?: string
  timeRange: { timelineStartTime: number; timelineEndTime: number }
  config: BasicTimelineConfig
  mediaType?: MediaType
}): UnifiedTimelineItemData {
  return reactive({
    id: generateUUID4(),
    mediaItemId: options.mediaItemId,
    trackId: options.trackId,
    timelineStatus: 'loading' as TimelineItemStatus,
    mediaType: options.mediaType || 'unknown',
    timeRange: options.timeRange,
    config: options.config
  })
}

// ==================== 状态转换行为函数 ====================

/**
 * 状态转换行为函数 - 无状态纯函数
 * 基于简化的3状态转换，状态显示信息通过关联媒体项目计算
 */
export async function transitionTimelineStatus(
  data: UnifiedTimelineItemData,
  newStatus: TimelineItemStatus
): Promise<void> {
  if (!canTransitionTo(data, newStatus)) {
    throw new Error(`Invalid timeline transition: ${data.timelineStatus} → ${newStatus}`)
  }

  const oldStatus = data.timelineStatus
  data.timelineStatus = newStatus // ✅ 自动触发响应式更新

  // 精灵生命周期管理 - 更清晰的3分支
  await handleSpriteLifecycle(data, oldStatus, newStatus)

  console.log(`✅ 时间轴项目状态转换: ${oldStatus} → ${newStatus}`)
}

/**
 * Sprite生命周期管理函数
 */
async function handleSpriteLifecycle(
  data: UnifiedTimelineItemData,
  oldStatus: TimelineItemStatus,
  newStatus: TimelineItemStatus
): Promise<void> {
  switch (newStatus) {
    case 'ready':
      // 只在ready时创建sprite，所有准备工作已完成
      if (oldStatus !== 'ready') {
        await createSprite(data)
      }
      break

    case 'loading':           // 保持现状不变
    case 'error':            // 清理资源
    default:
      if (oldStatus === 'ready') {
        await destroySprite(data)
      }
  }
}

/**
 * 创建Sprite函数
 */
async function createSprite(data: UnifiedTimelineItemData): Promise<void> {
  // 获取关联的媒体项目
  const mediaData = getMediaItemData(data.mediaItemId)
  if (!mediaData) {
    throw new Error('无法找到关联的媒体项目')
  }

  // 检查媒体项目是否就绪
  if (mediaData.mediaStatus !== 'ready') {
    throw new Error('关联的媒体项目尚未就绪')
  }

  // 通过 SpriteLifecycleManager 创建并添加 Sprite
  const spriteId = await SpriteLifecycleManager.createAndAddSprite(
    mediaData,
    data
  )

  data.spriteId = spriteId // ✅ 响应式更新
}

/**
 * 销毁Sprite函数
 */
async function destroySprite(data: UnifiedTimelineItemData): Promise<void> {
  if (!data.spriteId) {
    return
  }

  // 通过 SpriteLifecycleManager 移除 Sprite（AVCanvas 会自动销毁）
  await SpriteLifecycleManager.removeSprite(data.spriteId)
  data.spriteId = undefined // ✅ 响应式更新
}

// ==================== 查询函数 ====================

/**
 * 状态转换验证函数
 */
export function canTransitionTo(
  data: UnifiedTimelineItemData,
  newStatus: TimelineItemStatus
): boolean {
  // 3状态间的简单规则
  const VALID_TIMELINE_TRANSITIONS: Record<TimelineItemStatus, TimelineItemStatus[]> = {
    'loading': ['ready', 'error'],
    'ready': ['loading', 'error'],
    'error': ['loading']
  }

  const allowed = VALID_TIMELINE_TRANSITIONS[data.timelineStatus]
  return allowed?.includes(newStatus) ?? false
}

// ==================== 便捷操作函数 ====================

/**
 * 设置为加载状态
 */
export function setLoading(data: UnifiedTimelineItemData): Promise<void> {
  return transitionTimelineStatus(data, 'loading')
}

/**
 * 设置为就绪状态
 */
export function setReady(data: UnifiedTimelineItemData): Promise<void> {
  return transitionTimelineStatus(data, 'ready')
}

/**
 * 设置错误状态
 */
export function setError(data: UnifiedTimelineItemData): Promise<void> {
  return transitionTimelineStatus(data, 'error')
}
```

## 响应式状态转换流程示例

### 1. 本地文件处理流程（响应式版本）

```typescript
import { watch } from 'vue'
import { createMediaItemData, startMediaProcessing } from '@/unified/mediaitem'
import { createUserSelectedFileSourceData } from '@/unified/sources'
import { detectMediaType } from '@/utils'

// 用户拖拽本地文件到时间轴
async function handleLocalFileDrop(file: File, trackId: string) {
  // 1. 创建响应式媒体项目数据
  const mediaData = createMediaItemData({
    source: createUserSelectedFileSourceData(file),
    mediaType: detectMediaType(file)
  })

  // 2. 创建响应式时间轴项目数据
  const timelineData = createTimelineItemData({
    mediaItemId: mediaData.id,
    trackId,
    timeRange: { timelineStartTime: 0, timelineEndTime: 300 }, // 默认5秒
    config: createBasicTimelineConfig(file.name)
  })

  // 3. 开始媒体解析（自动触发UI更新）
  await startMediaProcessing(mediaData)

  // 4. 监听媒体状态变化，自动同步到时间轴
  watchMediaStatus(mediaData, timelineData)
}

// 响应式状态同步
function watchMediaStatus(
  mediaData: UnifiedMediaItemData,
  timelineData: UnifiedTimelineItemData
) {
  watch(() => mediaData.mediaStatus, async (newStatus) => {
    const targetTimelineStatus = MEDIA_TO_TIMELINE_STATUS_MAP[newStatus]

    if (timelineData.timelineStatus !== targetTimelineStatus) {
      await transitionTimelineStatus(timelineData, targetTimelineStatus)
    }
  }, { immediate: true })
}
```

**响应式流程特点**：
- ✅ **自动UI更新**：所有状态变化自动触发Vue组件重新渲染
- ✅ **类型安全**：完整的TypeScript类型检查
- ✅ **状态同步**：媒体状态自动映射到时间轴状态
- ✅ **简化架构**：移除复杂上下文模板，状态显示直接基于媒体项目计算

### 2. 远程文件下载流程（响应式版本）

```typescript
import { createRemoteFileSourceData } from '@/unified/sources'
import { extractFilename } from '@/utils'

// 输入远程URL创建时间轴项目
async function handleRemoteUrlAdd(url: string, trackId: string) {
  // 1. 创建响应式数据对象
  const mediaData = createMediaItemData({
    source: createRemoteFileSourceData(url),
    mediaType: 'unknown'
  })

  const timelineData = createTimelineItemData({
    mediaItemId: mediaData.id,
    trackId,
    timeRange: { timelineStartTime: 0, timelineEndTime: 300 },
    config: createBasicTimelineConfig(extractFilename(url))
  })

  // 2. 开始媒体处理（状态变化自动反映到UI）
  await startMediaProcessing(mediaData)

  // 3. 建立状态同步（自动更新进度条和状态显示）
  watchMediaStatus(mediaData, timelineData)
}
```

**响应式流程特点**：
- ✅ **实时进度**：下载进度自动更新到UI组件
- ✅ **状态驱动**：基于状态变化自动执行后续操作
- ✅ **错误处理**：网络错误自动反映到时间轴状态
- ✅ **简化逻辑**：无需手动管理下载进度上下文

### 3. 项目加载流程（响应式版本）

```typescript
// 加载项目时恢复时间轴项目
async function loadProjectTimelineItems(projectConfig: ProjectConfig) {
  const timelineItems: UnifiedTimelineItemData[] = []

  for (const itemConfig of projectConfig.timeline.timelineItems) {
    // 1. 创建响应式时间轴数据
    const timelineData = createTimelineItemData({
      mediaItemId: itemConfig.mediaItemId,
      trackId: itemConfig.trackId,
      timeRange: itemConfig.timeRange,
      config: itemConfig.config
    })

    // 2. 检查关联媒体项目状态
    const mediaData = getMediaItemData(itemConfig.mediaItemId)

    if (mediaData) {
      // 媒体项目存在，同步状态
      const timelineStatus = MEDIA_TO_TIMELINE_STATUS_MAP[mediaData.mediaStatus]
      await transitionTimelineStatus(timelineData, timelineStatus)

      // 建立状态同步
      watchMediaStatus(mediaData, timelineData)
    } else {
      // 媒体项目不存在，标记为错误
      await setError(timelineData)
    }

    timelineItems.push(timelineData)
  }

  return timelineItems
}
```

**响应式流程特点**：
- ✅ **状态恢复**：自动恢复时间轴项目的正确状态
- ✅ **依赖检查**：自动验证媒体项目依赖关系
- ✅ **错误标记**：缺失文件自动标记为错误状态
- ✅ **实时同步**：加载后继续保持状态同步

## 响应式Sprite生命周期管理

### 1. 响应式Sprite管理器

```typescript
import type { Raw } from 'vue'
import type { AVCanvas } from '@webav/av-canvas'
import { VideoVisibleSprite, ImageVisibleSprite, AudioVisibleSprite } from '@/utils/sprites'
import { generateUUID4 } from '@/utils'

/**
 * 响应式Sprite生命周期管理器
 * 基于数据驱动的Sprite管理，与响应式时间轴项目数据配合
 */
export class SpriteLifecycleManager {
  private avCanvas: AVCanvas
  private spriteRegistry = new Map<string, Raw<CustomSprite>>() // spriteId -> sprite映射

  constructor(avCanvas: AVCanvas) {
    this.avCanvas = avCanvas
  }

  /**
   * 创建并添加 Sprite 到 AVCanvas
   * 返回spriteId供时间轴项目数据引用
   */
  async createAndAddSprite(
    mediaData: UnifiedMediaItemData,
    timelineData: UnifiedTimelineItemData
  ): Promise<string> {
    const { mediaType, webav } = mediaData

    if (!webav) {
      throw new Error('媒体项目WebAV对象未就绪')
    }

    // 生成唯一的spriteId
    const spriteId = generateUUID4()

    // 检查是否已存在 Sprite
    if (timelineData.spriteId && this.spriteRegistry.has(timelineData.spriteId)) {
      console.warn(`时间轴项目 ${timelineData.id} 的 Sprite 已存在，先移除旧的`)
      await this.removeSprite(timelineData.spriteId)
    }

    let sprite: Raw<CustomSprite>

    // 根据媒体类型创建对应的 Sprite
    switch (mediaType) {
      case 'video':
        if (!webav.mp4Clip) throw new Error('视频WebAV对象缺失')
        sprite = new VideoVisibleSprite(webav.mp4Clip)
        break

      case 'image':
        if (!webav.imgClip) throw new Error('图片WebAV对象缺失')
        sprite = new ImageVisibleSprite(webav.imgClip)
        break

      case 'audio':
        if (!webav.audioClip) throw new Error('音频WebAV对象缺失')
        sprite = new AudioVisibleSprite(webav.audioClip)
        break

      default:
        throw new Error(`不支持的媒体类型: ${mediaType}`)
    }

    // 设置 Sprite 的基础属性
    await this.setupSpriteProperties(sprite, timelineData)

    // 添加到 AVCanvas
    await this.avCanvas.addSprite(sprite)

    // 注册到管理器
    this.spriteRegistry.set(spriteId, sprite)

    console.log(`✅ Sprite 已创建并添加到 AVCanvas: ${spriteId} (${mediaType})`)

    return spriteId
  }

  /**
   * 从 AVCanvas 移除 Sprite（AVCanvas 会自动销毁）
   */
  async removeSprite(spriteId: string): Promise<void> {
    const sprite = this.spriteRegistry.get(spriteId)
    if (!sprite) {
      console.warn(`未找到 Sprite: ${spriteId}`)
      return
    }

    try {
      // 从 AVCanvas 移除（AVCanvas 会自动销毁 Sprite）
      await this.avCanvas.removeSprite(sprite)

      // 从注册表移除
      this.spriteRegistry.delete(spriteId)

      console.log(`🗑️ Sprite 已从 AVCanvas 移除: ${spriteId}`)
    } catch (error) {
      console.error(`❌ Sprite 移除失败: ${spriteId}`, error)
      throw error
    }
  }

  /**
   * 设置Sprite基础属性
   */
  private async setupSpriteProperties(
    sprite: Raw<CustomSprite>,
    timelineData: UnifiedTimelineItemData
  ): Promise<void> {
    // 设置时间范围
    sprite.setTimeRange({
      timelineStartTime: timelineData.timeRange.timelineStartTime,
      timelineEndTime: timelineData.timeRange.timelineEndTime
    })

    // 设置基础变换属性
    const config = timelineData.config
    if (config.transform) {
      sprite.setTransform(config.transform)
    }

    // 设置其他属性...
  }

  /**
   * 获取Sprite实例（用于直接操作）
   */
  getSprite(spriteId: string): Raw<CustomSprite> | undefined {
    return this.spriteRegistry.get(spriteId)
  }

  /**
   * 更新Sprite属性（响应式更新）
   */
  async updateSpriteProperties(
    spriteId: string,
    updates: Partial<{
      timeRange: { timelineStartTime: number; timelineEndTime: number }
      transform: TransformData
      opacity: number
    }>
  ): Promise<void> {
    const sprite = this.spriteRegistry.get(spriteId)
    if (!sprite) {
      console.warn(`未找到 Sprite: ${spriteId}`)
      return
    }

    if (updates.timeRange) {
      sprite.setTimeRange(updates.timeRange)
    }

    if (updates.transform) {
      sprite.setTransform(updates.transform)
    }

    if (updates.opacity !== undefined) {
      sprite.setOpacity(updates.opacity)
    }
  }
}
```

### 2. 响应式状态同步管理

```typescript
import { watch, type WatchStopHandle } from 'vue'

/**
 * 响应式时间轴项目与媒体项目的状态同步管理器
 * 基于Vue3的watch机制实现自动状态同步
 */
export class TimelineMediaSyncManager {
  private timelineItems = new Map<string, UnifiedTimelineItemData>()
  private mediaItems = new Map<string, UnifiedMediaItemData>()
  private watchStopHandles = new Map<string, WatchStopHandle>() // 用于清理watch

  /**
   * 注册时间轴项目，建立与媒体项目的响应式关联
   */
  registerTimelineItem(timelineData: UnifiedTimelineItemData) {
    this.timelineItems.set(timelineData.id, timelineData)

    // 检查关联媒体项目的状态
    const mediaData = this.mediaItems.get(timelineData.mediaItemId)
    if (mediaData) {
      this.setupMediaStatusSync(timelineData, mediaData)
    } else {
      console.error(`❌ TimelineItem ${timelineData.id} 找不到关联的MediaItem ${timelineData.mediaItemId}`)
    }
  }

  /**
   * 建立媒体项目状态到时间轴项目状态的响应式同步
   */
  private setupMediaStatusSync(
    timelineData: UnifiedTimelineItemData,
    mediaData: UnifiedMediaItemData
  ) {
    // 使用Vue3的watch建立响应式同步
    const stopHandle = watch(
      () => mediaData.mediaStatus,
      async (newStatus) => {
        const targetTimelineStatus = MEDIA_TO_TIMELINE_STATUS_MAP[newStatus]

        if (timelineData.timelineStatus !== targetTimelineStatus) {
          // 触发时间轴项目状态转换（自动触发UI更新）
          await transitionTimelineStatus(timelineData, targetTimelineStatus)

          console.log(`🔄 状态同步: MediaItem(${newStatus}) → TimelineItem(${targetTimelineStatus})`)
        }
      },
      { immediate: true } // 立即执行一次同步当前状态
    )

    // 保存停止句柄，用于清理
    this.watchStopHandles.set(timelineData.id, stopHandle)
  }

  /**
   * 响应式时间轴项目工厂方法
   * 确保只有ready状态的MediaItem才能直接创建ready的TimelineItem
   */
  createTimelineItem(
    mediaItemId: string,
    trackId: string,
    timeRange: { timelineStartTime: number; timelineEndTime: number },
    config: BasicTimelineConfig
  ): UnifiedTimelineItemData | null {
    const mediaData = this.mediaItems.get(mediaItemId)

    if (!mediaData) {
      console.error(`❌ 找不到MediaItem: ${mediaItemId}`)
      return null
    }

    // 创建响应式TimelineItem数据
    const timelineData = createTimelineItemData({
      mediaItemId,
      trackId,
      timeRange,
      config,
      mediaType: mediaData.mediaType
    })

    // 根据MediaItem状态设置初始状态
    const initialStatus = MEDIA_TO_TIMELINE_STATUS_MAP[mediaData.mediaStatus]

    // 同步设置初始状态（不需要await，因为是同步操作）
    timelineData.timelineStatus = initialStatus

    // 注册到管理器（建立响应式同步）
    this.registerTimelineItem(timelineData)

    console.log(`✅ 创建时间轴项目: ${timelineData.id}, 初始状态: ${initialStatus}`)

    return timelineData
  }

  /**
   * 注册媒体项目
   */
  registerMediaItem(mediaData: UnifiedMediaItemData) {
    this.mediaItems.set(mediaData.id, mediaData)
  }

  /**
   * 清理时间轴项目（停止响应式监听）
   */
  unregisterTimelineItem(timelineId: string) {
    // 停止watch监听
    const stopHandle = this.watchStopHandles.get(timelineId)
    if (stopHandle) {
      stopHandle()
      this.watchStopHandles.delete(timelineId)
    }

    // 从注册表移除
    this.timelineItems.delete(timelineId)

    console.log(`🗑️ 清理时间轴项目: ${timelineId}`)
  }
}
```

## Vue组件中的使用示例

### 1. 时间轴项目组件

```vue
<template>
  <div class="timeline-item" :class="statusClass">
    <!-- 基础信息显示 -->
    <div class="item-header">
      <span class="item-name">{{ timelineData.config.name }}</span>
      <span class="item-status">{{ statusInfo.text }}</span>
    </div>

    <!-- 进度显示 -->
    <div v-if="statusInfo.hasProgress" class="progress-bar">
      <div
        class="progress-fill"
        :style="{ width: `${statusInfo.percent}%` }"
      ></div>
      <span class="progress-text">{{ progressText }}</span>
    </div>

    <!-- 错误信息显示 -->
    <div v-if="statusInfo.hasError" class="error-info">
      <span class="error-message">{{ statusInfo.errorMessage }}</span>
      <button v-if="statusInfo.recoverable" @click="handleRetry">重试</button>
    </div>

    <!-- Sprite控制 -->
    <div v-if="timelineData.spriteId" class="sprite-controls">
      <button @click="updateOpacity(0.5)">半透明</button>
      <button @click="updateOpacity(1.0)">不透明</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject } from 'vue'
import type { UnifiedTimelineItemData } from '@/unified/timelineitem'
import { TimelineStatusDisplayUtils } from '@/unified/timelineitem'
import { useUnifiedStore } from '@/unified/unifiedStore'
import { setLoading } from '@/unified/timelineitem/behaviors'

// 注入依赖
const spriteManager = inject<SpriteLifecycleManager>('spriteManager')!
const unifiedStore = useUnifiedStore()

// Props
interface Props {
  timelineData: UnifiedTimelineItemData
}
const props = defineProps<Props>()

// 计算属性 - 自动响应数据变化
const statusClass = computed(() => {
  return `status-${props.timelineData.timelineStatus}`
})

// 基于关联媒体项目计算状态显示信息
const mediaData = computed(() => unifiedStore.getMediaItem(props.timelineData.mediaItemId))

const statusInfo = computed(() =>
  TimelineStatusDisplayUtils.getStatusDisplayInfo(mediaData.value)
)

const progressText = computed(() => {
  if (!statusInfo.value.hasProgress) return ''

  return statusInfo.value.speed
    ? `${statusInfo.value.percent}% (${statusInfo.value.speed})`
    : `${statusInfo.value.percent}%`
})

// 方法
async function handleRetry() {
  // 重新开始处理流程
  await setLoading(props.timelineData)
}

async function updateOpacity(opacity: number) {
  if (!props.timelineData.spriteId) return

  await spriteManager.updateSpriteProperties(props.timelineData.spriteId, {
    opacity
  })
}
</script>
```

### 2. 时间轴管理组件

```vue
<template>
  <div class="timeline-manager">
    <div class="timeline-tracks">
      <div
        v-for="track in tracks"
        :key="track.id"
        class="track"
      >
        <div class="track-header">{{ track.name }}</div>
        <div class="track-items">
          <TimelineItemComponent
            v-for="item in getTrackItems(track.id)"
            :key="item.id"
            :timeline-data="item"
            @update="handleItemUpdate"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, provide, onMounted, onUnmounted, inject } from 'vue'
import type { UnifiedTimelineItemData } from '@/unified/timelineitem'
import { TimelineMediaSyncManager, SpriteLifecycleManager } from '@/unified/timelineitem'
import type { AVCanvas } from '@webav/av-canvas'

// 注入AVCanvas
const avCanvas = inject<AVCanvas>('avCanvas')!

// 响应式数据
const timelineItems = ref<UnifiedTimelineItemData[]>([])
const tracks = ref([
  { id: 'video-1', name: '视频轨道 1' },
  { id: 'audio-1', name: '音频轨道 1' }
])

// 管理器实例
const syncManager = new TimelineMediaSyncManager()
const spriteManager = new SpriteLifecycleManager(avCanvas)

// 提供给子组件
provide('syncManager', syncManager)
provide('spriteManager', spriteManager)

// 计算属性
const getTrackItems = computed(() => (trackId: string) => {
  return timelineItems.value.filter(item => item.trackId === trackId)
})

// 方法
function handleItemUpdate(itemData: UnifiedTimelineItemData) {
  // 响应式数据自动更新UI，无需手动处理
  console.log('时间轴项目已更新:', itemData.id)
}

// 生命周期
onMounted(() => {
  // 初始化时间轴项目
  loadTimelineItems()
})

onUnmounted(() => {
  // 清理所有watch监听
  timelineItems.value.forEach(item => {
    syncManager.unregisterTimelineItem(item.id)
  })
})

async function loadTimelineItems() {
  // 从项目配置加载时间轴项目
  const items = await loadProjectTimelineItems(projectConfig)
  timelineItems.value = items
}
</script>
```

## 响应式重构的优势总结

### 1. 架构一致性
- **统一模式**：数据源、媒体项目、时间轴项目都采用"核心数据 + 行为分离"
- **响应式支持**：所有状态变化自动触发Vue组件更新
- **简化设计**：移除复杂的上下文模板系统，状态显示直接基于媒体项目计算

### 2. 开发体验提升
- **自动更新**：无需手动调用UI更新方法
- **调试便利**：Vue DevTools可以直接查看所有状态变化
- **代码简洁**：消除了复杂的上下文管理和模板系统

### 3. 维护性改善
- **职责清晰**：数据、行为、UI完全分离
- **易于测试**：纯函数易于单元测试
- **状态一致性**：状态显示直接基于数据源，避免不一致问题

### 4. 性能优化
- **精确更新**：Vue3的响应式系统只更新变化的部分
- **内存管理**：自动清理不再使用的watch监听
- **减少计算**：移除复杂的上下文计算，直接使用媒体项目状态

## 实现建议

### 1. 迁移策略
1. **移除旧系统**：删除所有 statusContext 和上下文模板相关代码
2. **更新组件**：改为使用 TimelineStatusDisplayUtils 计算状态显示
3. **简化接口**：时间轴项目数据结构只保留核心属性
4. **测试验证**：确保状态显示功能正常工作

### 2. 测试重点
- **状态显示测试**：验证基于媒体项目状态的显示计算
- **响应式更新测试**：验证状态变化自动触发UI更新
- **状态同步测试**：验证媒体项目与时间轴项目状态同步
- **Sprite生命周期测试**：验证Sprite的创建、更新、销毁

---

*文档更新时间：2025-01-27*
*基于响应式重构版本：v2.0 - 移除上下文模板系统*
*关联文档：16-Vue3响应式重构方案-核心数据与行为分离.md, 10-统一时间轴项目设计-类型设计.md*
