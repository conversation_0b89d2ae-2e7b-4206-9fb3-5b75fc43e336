# 统一轨道类型设计 - 使用示例

## 概述

本文档展示了基于新架构的统一轨道类型系统的具体使用方法。新的轨道系统采用"核心数据 + 行为分离"的响应式架构模式，与UnifiedMediaItem和UnifiedTimelineItem保持一致的设计理念。

## 核心设计特点

### 1. 响应式优先
- 采用Vue3响应式系统，状态变化自动触发UI更新
- 纯数据对象设计，使用`reactive()`包装
- 与现有的统一媒体项目和时间轴项目架构一致

### 2. 类型安全
- 完整的TypeScript类型定义
- 类型守卫函数确保运行时类型安全
- 编译时错误检查

### 3. 功能丰富
- 支持5种轨道类型：视频、音频、文本、字幕、特效
- 完整的状态管理：可见性、音频、布局、渲染配置
- 灵活的元数据系统

## 基础使用示例

### 1. 创建轨道管理器

```typescript
import { createUnifiedTrackManager } from '@/unified/track'

// 创建轨道管理器实例
const trackManager = createUnifiedTrackManager()

// 获取响应式轨道列表
const tracks = trackManager.tracks

// 监听轨道变化
watch(tracks, (newTracks) => {
  console.log('轨道列表更新:', newTracks.length)
}, { deep: true })
```

### 2. 创建不同类型的轨道

```typescript
import { createUnifiedTrackData, TrackTypes } from '@/unified/track'

// 创建视频轨道
const videoTrack = createUnifiedTrackData('video', '主视频轨道', {
  layout: { height: 100 },
  metadata: { color: '#4CAF50' }
})

// 创建音频轨道
const audioTrack = createUnifiedTrackData('audio', '背景音乐', {
  audio: { volume: 0.8 },
  layout: { height: 60 }
})

// 创建文本轨道
const textTrack = createUnifiedTrackData('text', '标题文字', {
  renderConfig: {
    fontFamily: 'Arial',
    fontSize: 24,
    fontColor: '#FFFFFF'
  }
})
```

### 3. 轨道管理操作

```typescript
// 添加轨道到管理器
const result = trackManager.createTrack('video', '新视频轨道')
if (result.success) {
  console.log('轨道创建成功:', result.trackId)
}

// 更新轨道属性
trackManager.updateTrack(result.trackId!, {
  name: '更新后的轨道名称',
  layout: { height: 80 }
})

// 删除轨道
trackManager.deleteTrack(result.trackId!)
```

## 轨道状态管理示例

### 1. 可见性控制

```typescript
import { toggleTrackVisibility, isTrackVisible } from '@/unified/track'

// 切换轨道可见性
toggleTrackVisibility(videoTrack)

// 检查轨道是否可见
if (isTrackVisible(videoTrack)) {
  console.log('轨道当前可见')
}

// 设置透明度
setTrackOpacity(videoTrack, 0.5)
```

### 2. 音频控制

```typescript
import { 
  toggleTrackMute, 
  toggleTrackSolo, 
  setTrackVolume,
  trackSupportsAudio 
} from '@/unified/track'

// 检查轨道是否支持音频
if (trackSupportsAudio(audioTrack)) {
  // 切换静音
  toggleTrackMute(audioTrack)
  
  // 切换独奏
  toggleTrackSolo(audioTrack)
  
  // 设置音量
  setTrackVolume(audioTrack, 0.8)
}
```

### 3. 轨道锁定和状态

```typescript
import { toggleTrackLock, isTrackLocked } from '@/unified/track'

// 锁定轨道
toggleTrackLock(videoTrack)

// 检查锁定状态
if (isTrackLocked(videoTrack)) {
  console.log('轨道已锁定，无法编辑')
}

// 设置轨道状态
videoTrack.status = 'disabled'
```

## 轨道查询和筛选示例

### 1. 基础查询

```typescript
// 根据ID获取轨道
const track = trackManager.getTrackById('track_123')

// 根据类型获取轨道
const videoTracks = trackManager.getTracksByType('video')
const audioTracks = trackManager.getTracksByType('audio')
```

### 2. 条件查询

```typescript
// 查询可见的视频轨道
const visibleVideoTracks = trackManager.queryTracks({
  type: 'video',
  visible: true
})

// 查询未静音的音频轨道
const unmutedAudioTracks = trackManager.queryTracks({
  type: 'audio',
  muted: false
})

// 查询锁定的轨道
const lockedTracks = trackManager.queryTracks({
  locked: true
})
```

### 3. 轨道统计

```typescript
// 获取轨道统计信息
const stats = trackManager.stats

console.log('轨道统计:', {
  总数: stats.total,
  视频轨道: stats.byType.video,
  音频轨道: stats.byType.audio,
  可见轨道: stats.visible,
  静音轨道: stats.muted
})
```

## 轨道排序和布局示例

### 1. 轨道重排序

```typescript
// 重新排序轨道
const newOrder = ['track_1', 'track_3', 'track_2']
const result = trackManager.reorderTracks(newOrder)

if (result.success) {
  console.log('轨道重排序成功')
}
```

### 2. 轨道高度调整

```typescript
import { setTrackHeight, validateTrackHeight } from '@/unified/track'

// 验证高度值
const validation = validateTrackHeight(120)
if (validation.valid) {
  setTrackHeight(videoTrack, 120)
} else {
  console.error('高度值无效:', validation.error)
}
```

### 3. 轨道折叠

```typescript
import { toggleTrackCollapse } from '@/unified/track'

// 切换轨道折叠状态
toggleTrackCollapse(videoTrack)

// 检查折叠状态
if (videoTrack.layout.isCollapsed) {
  console.log('轨道已折叠')
}
```

## 批量操作示例

### 1. 批量更新轨道

```typescript
// 批量更新多个轨道
const trackIds = ['track_1', 'track_2', 'track_3']
const updates = {
  visibility: { isVisible: false },
  audio: { isMuted: true }
}

const result = trackManager.batchUpdateTracks(trackIds, updates)
console.log('批量更新结果:', result.success)
```

### 2. 重置所有轨道

```typescript
// 重置所有轨道到默认状态
const result = trackManager.resetAllTracks()
if (result.success) {
  console.log('所有轨道已重置')
}
```

## Vue组件集成示例

### 1. 轨道列表组件

```vue
<template>
  <div class="track-list">
    <div 
      v-for="track in tracks" 
      :key="track.id"
      class="track-item"
      :class="{
        'track-visible': isTrackVisible(track),
        'track-muted': isTrackMuted(track),
        'track-locked': isTrackLocked(track)
      }"
      :style="{ 
        height: track.layout.height + 'px',
        backgroundColor: getTrackColor(track)
      }"
    >
      <!-- 轨道头部 -->
      <div class="track-header">
        <span class="track-icon">{{ getTrackIcon(track) }}</span>
        <span class="track-name">{{ getTrackDisplayName(track) }}</span>
        
        <!-- 轨道控制按钮 -->
        <div class="track-controls">
          <button @click="toggleTrackVisibility(track)">
            {{ track.visibility.isVisible ? '👁️' : '🙈' }}
          </button>
          
          <button 
            v-if="trackSupportsAudio(track)"
            @click="toggleTrackMute(track)"
          >
            {{ track.audio.isMuted ? '🔇' : '🔊' }}
          </button>
          
          <button @click="toggleTrackLock(track)">
            {{ isTrackLocked(track) ? '🔒' : '🔓' }}
          </button>
        </div>
      </div>
      
      <!-- 轨道内容区域 -->
      <div class="track-content">
        <!-- 这里放置时间轴项目 -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  createUnifiedTrackManager,
  isTrackVisible,
  isTrackMuted,
  isTrackLocked,
  trackSupportsAudio,
  toggleTrackVisibility,
  toggleTrackMute,
  toggleTrackLock,
  getTrackDisplayName,
  getTrackColor,
  getTrackIcon
} from '@/unified/track'

// 创建轨道管理器
const trackManager = createUnifiedTrackManager()

// 响应式轨道列表
const tracks = computed(() => trackManager.tracks.value)

// 初始化默认轨道
trackManager.createTrack('video', '视频轨道 1')
trackManager.createTrack('audio', '音频轨道 1')
trackManager.createTrack('text', '文本轨道 1')
</script>
```

### 2. 轨道属性面板组件

```vue
<template>
  <div v-if="selectedTrack" class="track-properties">
    <h3>轨道属性</h3>
    
    <!-- 基础信息 -->
    <div class="property-group">
      <label>轨道名称:</label>
      <input 
        v-model="selectedTrack.name" 
        @input="updateTrack"
        :disabled="isTrackLocked(selectedTrack)"
      />
    </div>
    
    <!-- 可见性控制 -->
    <div class="property-group">
      <label>可见性:</label>
      <input 
        type="checkbox" 
        v-model="selectedTrack.visibility.isVisible"
        @change="updateTrack"
      />
      
      <label>透明度:</label>
      <input 
        type="range" 
        min="0" 
        max="1" 
        step="0.1"
        v-model.number="selectedTrack.visibility.opacity"
        @input="updateTrack"
      />
    </div>
    
    <!-- 音频控制 (仅音频轨道) -->
    <div v-if="trackSupportsAudio(selectedTrack)" class="property-group">
      <label>音量:</label>
      <input 
        type="range" 
        min="0" 
        max="2" 
        step="0.1"
        v-model.number="selectedTrack.audio.volume"
        @input="updateTrack"
      />
      
      <label>静音:</label>
      <input 
        type="checkbox" 
        v-model="selectedTrack.audio.isMuted"
        @change="updateTrack"
      />
    </div>
    
    <!-- 布局控制 -->
    <div class="property-group">
      <label>轨道高度:</label>
      <input 
        type="number" 
        min="30" 
        max="200"
        v-model.number="selectedTrack.layout.height"
        @input="updateTrack"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { UnifiedTrackData } from '@/unified/track'
import { 
  isTrackLocked,
  trackSupportsAudio,
  validateTrackData
} from '@/unified/track'

// Props
const props = defineProps<{
  track: UnifiedTrackData | null
}>()

// 选中的轨道
const selectedTrack = ref(props.track)

// 监听轨道变化
watch(() => props.track, (newTrack) => {
  selectedTrack.value = newTrack
})

// 更新轨道
function updateTrack() {
  if (!selectedTrack.value) return
  
  // 验证数据
  const validation = validateTrackData(selectedTrack.value)
  if (!validation.valid) {
    console.error('轨道数据验证失败:', validation.errors)
    return
  }
  
  // 更新时间戳
  selectedTrack.value.updatedAt = new Date().toISOString()
  
  console.log('轨道属性已更新')
}
</script>
```

## 性能优化建议

### 1. 响应式优化

```typescript
// 使用 shallowRef 优化大量轨道的性能
import { shallowRef } from 'vue'

const tracks = shallowRef<UnifiedTrackData[]>([])

// 批量更新时使用 nextTick
import { nextTick } from 'vue'

async function batchUpdateTracks() {
  // 批量修改
  tracks.value.forEach(track => {
    track.visibility.isVisible = false
  })
  
  // 等待DOM更新
  await nextTick()
  console.log('批量更新完成')
}
```

### 2. 内存管理

```typescript
// 清理轨道资源
function cleanupTrack(track: UnifiedTrackData) {
  // 清理渲染配置
  track.renderConfig = {}
  
  // 重置状态
  track.status = 'disabled'
  
  console.log('轨道资源已清理')
}
```

## 总结

新的统一轨道类型系统提供了：

1. **完整的类型安全**：TypeScript类型定义和运行时验证
2. **响应式架构**：与Vue3完美集成，自动UI更新
3. **丰富的功能**：支持多种轨道类型和复杂的状态管理
4. **易于使用**：简洁的API和完善的工具函数
5. **高性能**：优化的响应式设计和批量操作支持

这个设计与重构文档中的统一异步源架构完全一致，为视频编辑器提供了强大而灵活的轨道管理能力。
