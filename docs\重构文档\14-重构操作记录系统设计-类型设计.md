# 重构操作记录系统设计 - 类型接口设计

## 概述

基于AI视频编辑器的重构架构（统一媒体类型和时间轴项目设计），本文档设计了新的操作记录系统的核心类型接口，以支持UnifiedMediaItem和UnifiedTimelineItem的撤销/重做操作。新系统将与重构后的状态驱动架构完美集成，提供类型安全、扩展性强的操作记录能力。

## 当前操作记录系统分析

### 1. 现有系统优势

- **命令模式实现**：清晰的SimpleCommand接口，支持execute/undo操作
- **批量操作支持**：BaseBatchCommand和BatchBuilder提供批量操作能力
- **类型安全**：完整的TypeScript类型定义
- **历史管理**：SimpleHistoryManager提供撤销/重做栈管理
- **通知集成**：与NotificationManager集成，提供用户反馈

### 2. 现有系统问题

- **双重类型复杂性**：需要处理LocalTimelineItem和AsyncProcessingTimelineItem两套类型
- **状态管理分散**：操作记录与状态管理逻辑耦合
- **重建逻辑复杂**："从源头重建"原则导致复杂的数据保存和恢复逻辑
- **扩展性限制**：难以支持新的媒体类型和数据源

## 重构操作记录系统设计

### 1. 核心设计理念

**状态驱动的操作记录**：
- 操作记录不再直接操作数据，而是触发状态转换
- 利用重构后的状态机模式，确保操作的一致性和可预测性
- 通过TransitionContext记录操作上下文，支持精确的撤销/重做

**统一的操作接口**：
- 基于UnifiedMediaItem和UnifiedTimelineItem的统一操作接口
- 消除双重类型系统带来的复杂性
- 支持数据源无关的操作记录

### 2. 新的命令接口设计

```typescript
/**
 * 重构后的统一命令接口
 * 基于状态转换的操作记录，支持丰富的上下文信息
 */
export interface UnifiedCommand {
  // 基础属性
  id: string
  description: string
  timestamp: number
  
  // 操作类型标识
  operationType: OperationType
  
  // 目标对象信息
  targetInfo: CommandTargetInfo
  
  // 状态转换信息
  stateTransition: StateTransitionInfo
  
  // 执行和撤销方法
  execute(): Promise<CommandResult>
  undo(): Promise<CommandResult>
  
  // 可选的验证方法
  canExecute?(): boolean
  canUndo?(): boolean
  
  // 操作合并支持（用于优化历史记录）
  canMergeWith?(other: UnifiedCommand): boolean
  mergeWith?(other: UnifiedCommand): UnifiedCommand
}

/**
 * 操作类型枚举
 */
export type OperationType =
  // 时间轴项目操作
  | 'timeline.create' | 'timeline.delete' | 'timeline.move' | 'timeline.split' | 'timeline.duplicate'
  // 属性操作
  | 'property.update' | 'property.batch_update'
  // 关键帧操作
  | 'keyframe.create' | 'keyframe.delete' | 'keyframe.update' | 'keyframe.clear'
  // 轨道操作
  | 'track.create' | 'track.delete' | 'track.reorder'
  // 选择操作
  | 'selection.change'
  // 批量操作
  | 'batch.operation'

/**
 * 命令目标信息
 */
export interface CommandTargetInfo {
  type: 'timeline' | 'track' | 'selection' | 'batch'
  ids: string[] // 目标对象ID列表
  metadata?: Record<string, any> // 额外的目标信息
}

/**
 * 状态转换信息
 */
export interface StateTransitionInfo {
  // 前置状态快照
  beforeState: StateSnapshot
  // 后置状态快照（执行后填充）
  afterState?: StateSnapshot
  // 转换上下文
  transitionContext?: TransitionContext
}

/**
 * 状态快照
 */
export interface StateSnapshot {
  // 时间轴项目状态快照
  timelineItems?: Record<string, TimelineItemSnapshot>
  // 轨道状态快照
  tracks?: Record<string, TrackSnapshot>
  // 选择状态快照
  selection?: SelectionSnapshot
}

/**
 * 命令执行结果
 */
export interface CommandResult {
  success: boolean
  error?: string
  affectedItems?: string[]
  stateChanges?: StateSnapshot
}

/**
 * 时间轴项目状态快照
 */
export interface TimelineItemSnapshot {
  id: string
  mediaItemId: string
  trackId?: string
  timelineStatus: TimelineItemStatus
  statusContext?: TimelineStatusContext
  timeRange: {
    timelineStartTime: number
    timelineEndTime: number
  }
  config: any // 序列化的配置
  hasSprite: boolean
}

/**
 * 轨道状态快照
 */
export interface TrackSnapshot {
  id: string
  name: string
  type: TrackType
  isVisible: boolean
  isMuted: boolean
  height: number
  order: number
}

/**
 * 选择状态快照
 */
export interface SelectionSnapshot {
  selectedTimelineItemIds: string[]
  selectedTrackIds: string[]
  focusedItemId?: string
}



/**
 * 统一时间轴模块接口
 */
export interface UnifiedTimelineModule {
  getTimelineItem(id: string): UnifiedTimelineItem | undefined
  createTimelineItem(config: TimelineItemConfig): Promise<UnifiedTimelineItem>
  removeTimelineItem(id: string): Promise<void>
  splitTimelineItem(item: UnifiedTimelineItem, splitTime: number): Promise<{
    firstItem: UnifiedTimelineItem
    secondItem: UnifiedTimelineItem
  }>
  restoreTimelineItemFromSnapshot(snapshot: TimelineItemSnapshot): Promise<UnifiedTimelineItem>
  getAllTimelineItems(): UnifiedTimelineItem[]
}

/**
 * Sprite生命周期管理器接口
 */
export interface SpriteLifecycleManager {
  createSprite(timelineItem: UnifiedTimelineItem): Promise<void>
  destroySprite(timelineItem: UnifiedTimelineItem): Promise<void>
  updateSprite(timelineItem: UnifiedTimelineItem): Promise<void>
}

/**
 * 时间轴项目创建配置
 */
export interface TimelineItemConfig {
  mediaItemId: string
  trackId: string
  timeRange: {
    timelineStartTime: number
    timelineEndTime: number
  }
  config: BasicTimelineConfig
}
```

### 3. 时间轴项目操作命令基类

```typescript
/**
 * 统一时间轴项目操作命令基类
 * 基于UnifiedTimelineItem的状态转换实现操作记录
 */
export abstract class UnifiedTimelineCommand implements UnifiedCommand {
  public readonly id: string
  public readonly description: string
  public readonly timestamp: number
  public readonly operationType: OperationType
  public readonly targetInfo: CommandTargetInfo
  public readonly stateTransition: StateTransitionInfo

  constructor(
    operationType: OperationType,
    targetIds: string[],
    description: string,
    protected timelineModule: UnifiedTimelineModule,
    protected spriteManager: SpriteLifecycleManager
  ) {
    this.id = generateCommandId()
    this.description = description
    this.timestamp = Date.now()
    this.operationType = operationType
    this.targetInfo = {
      type: 'timeline',
      ids: targetIds
    }

    this.stateTransition = {
      beforeState: this.createStateSnapshot()
    }
  }

  /**
   * 创建时间轴状态快照
   */
  protected createStateSnapshot(): StateSnapshot {
    const timelineItems: Record<string, TimelineItemSnapshot> = {}

    for (const id of this.targetInfo.ids) {
      const item = this.timelineModule.getTimelineItem(id)
      if (item) {
        timelineItems[id] = {
          id: item.id,
          mediaItemId: item.mediaItemId,
          trackId: item.trackId,
          timelineStatus: item.timelineStatus,
          statusContext: item.statusContext,
          timeRange: { ...item.timeRange },
          config: this.serializeConfig(item.config),
          hasSprite: !!item.sprite
        }
      }
    }

    return { timelineItems }
  }

  protected serializeConfig(config: BasicTimelineConfig): any {
    return {
      name: config.name,
      mediaConfig: { ...config.mediaConfig },
      animation: config.animation ? { ...config.animation } : undefined
    }
  }

  abstract execute(): Promise<CommandResult>
  abstract undo(): Promise<CommandResult>
}
```

### 4. 批量操作命令接口

```typescript
/**
 * 统一批量命令
 * 支持多种类型命令的批量执行，提供智能的撤销/重做
 */
export class UnifiedBatchCommand implements UnifiedCommand {
  public readonly id: string
  public readonly description: string
  public readonly timestamp: number
  public readonly operationType: OperationType = 'batch.operation'
  public readonly targetInfo: CommandTargetInfo
  public readonly stateTransition: StateTransitionInfo

  private subCommands: UnifiedCommand[] = []
  private executedCommands: UnifiedCommand[] = []

  constructor(
    description: string,
    commands: UnifiedCommand[]
  ) {
    this.id = generateCommandId()
    this.description = description
    this.timestamp = Date.now()
    this.subCommands = [...commands]

    // 收集所有目标信息
    this.targetInfo = this.collectTargetInfo()

    // 创建合并的状态快照
    this.stateTransition = {
      beforeState: this.createMergedStateSnapshot()
    }
  }

  private collectTargetInfo(): CommandTargetInfo {
    const allIds = new Set<string>()
    const types = new Set<string>()

    for (const cmd of this.subCommands) {
      cmd.targetInfo.ids.forEach(id => allIds.add(id))
      types.add(cmd.targetInfo.type)
    }

    return {
      type: types.size === 1 ? Array.from(types)[0] as any : 'batch',
      ids: Array.from(allIds)
    }
  }

  private createMergedStateSnapshot(): StateSnapshot {
    const merged: StateSnapshot = {
      timelineItems: {},
      tracks: {},
      selection: undefined
    }

    for (const cmd of this.subCommands) {
      const snapshot = cmd.stateTransition.beforeState

      // 合并各类型的状态快照
      if (snapshot.timelineItems) {
        Object.assign(merged.timelineItems!, snapshot.timelineItems)
      }
      if (snapshot.tracks) {
        Object.assign(merged.tracks!, snapshot.tracks)
      }
      if (snapshot.selection) {
        merged.selection = snapshot.selection
      }
    }

    return merged
  }

  async execute(): Promise<CommandResult> {
    // 实现批量执行逻辑
    // 详细实现见使用示例文档
  }

  async undo(): Promise<CommandResult> {
    // 实现批量撤销逻辑
    // 详细实现见使用示例文档
  }

  canExecute(): boolean {
    return this.subCommands.every(cmd => cmd.canExecute?.() !== false)
  }

  canUndo(): boolean {
    return this.executedCommands.every(cmd => cmd.canUndo?.() !== false)
  }

  /**
   * 获取批量操作摘要
   */
  getBatchSummary(): string {
    return `${this.description} (${this.subCommands.length}个操作)`
  }
}
```

### 5. 命令合并器接口

```typescript
/**
 * 命令合并器
 * 提供智能的命令合并逻辑，优化历史记录
 */
export class CommandMerger {
  /**
   * 检查两个命令是否可以合并
   */
  static canMerge(cmd1: UnifiedCommand, cmd2: UnifiedCommand): boolean {
    // 基础合并条件
    if (cmd1.operationType !== cmd2.operationType) return false
    if (cmd1.targetInfo.type !== cmd2.targetInfo.type) return false

    // 时间窗口检查（5秒内的操作可以合并）
    const timeDiff = Math.abs(cmd2.timestamp - cmd1.timestamp)
    if (timeDiff > 5000) return false

    // 特定操作类型的合并逻辑
    switch (cmd1.operationType) {
      case 'property.update':
        return this.canMergePropertyUpdates(cmd1, cmd2)
      case 'timeline.move':
        return this.canMergeTimelineMoves(cmd1, cmd2)
      default:
        return cmd1.canMergeWith?.(cmd2) || false
    }
  }

  /**
   * 合并两个命令
   */
  static merge(cmd1: UnifiedCommand, cmd2: UnifiedCommand): UnifiedCommand {
    if (!this.canMerge(cmd1, cmd2)) {
      throw new Error('命令无法合并')
    }

    return cmd1.mergeWith!(cmd2)
  }

  private static canMergePropertyUpdates(cmd1: UnifiedCommand, cmd2: UnifiedCommand): boolean {
    // 相同目标的属性更新可以合并
    return cmd1.targetInfo.ids.length === 1 &&
           cmd2.targetInfo.ids.length === 1 &&
           cmd1.targetInfo.ids[0] === cmd2.targetInfo.ids[0]
  }

  private static canMergeTimelineMoves(cmd1: UnifiedCommand, cmd2: UnifiedCommand): boolean {
    // 相同项目的连续移动可以合并
    return cmd1.targetInfo.ids.length === 1 &&
           cmd2.targetInfo.ids.length === 1 &&
           cmd1.targetInfo.ids[0] === cmd2.targetInfo.ids[0]
  }
}
```

### 6. 重构历史管理器接口

```typescript
/**
 * 重构后的统一历史管理器
 * 支持UnifiedCommand接口和智能命令合并
 */
export class UnifiedHistoryManager {
  private commands: UnifiedCommand[] = []
  private currentIndex = -1
  private maxHistorySize = 100

  constructor(
    private notificationManager: NotificationManager,
    private commandMerger: CommandMerger = new CommandMerger()
  ) {}

  /**
   * 执行命令并添加到历史记录
   */
  async executeCommand(command: UnifiedCommand): Promise<void> {
    // 详细实现见使用示例文档
  }

  /**
   * 撤销上一个命令
   */
  async undo(): Promise<boolean> {
    // 详细实现见使用示例文档
  }

  /**
   * 重做下一个命令
   */
  async redo(): Promise<boolean> {
    // 详细实现见使用示例文档
  }

  canUndo(): boolean {
    return this.currentIndex >= 0
  }

  canRedo(): boolean {
    return this.currentIndex < this.commands.length - 1
  }

  clear(): void {
    this.commands = []
    this.currentIndex = -1
    console.log('🗑️ 历史记录已清空')
  }

  /**
   * 获取历史记录摘要
   */
  getHistorySummary() {
    return {
      totalCommands: this.commands.length,
      currentIndex: this.currentIndex,
      canUndo: this.canUndo(),
      canRedo: this.canRedo(),
      commands: this.commands.map((cmd, index) => ({
        id: cmd.id,
        description: cmd.description,
        operationType: cmd.operationType,
        timestamp: cmd.timestamp,
        isCurrent: index === this.currentIndex,
        isExecuted: index <= this.currentIndex,
        targetCount: cmd.targetInfo.ids.length
      }))
    }
  }
}
```

## 设计优势

### 1. 架构统一
- **消除双重类型**：基于UnifiedMediaItem和UnifiedTimelineItem的统一操作
- **状态驱动**：利用重构后的状态机模式，确保操作一致性
- **上下文丰富**：通过TransitionContext提供详细的操作上下文

### 2. 类型安全和可维护性
- **完整的TypeScript支持**：所有接口都有严格的类型定义
- **编译时错误检查**：类型系统可以在编译时捕获大部分错误
- **智能代码提示**：IDE可以提供准确的代码补全和文档

### 3. 扩展性和灵活性
- **插件化命令系统**：新的操作类型可以轻松扩展
- **数据源无关**：支持任意类型的数据源操作记录
- **批量操作支持**：统一的批量命令处理机制

---

*文档创建时间：2025-01-19*
*基于重构文档版本：v1.0*
*关联文档：07-媒体类型统一设计-类型设计.md, 10-统一时间轴项目设计-类型设计.md*
