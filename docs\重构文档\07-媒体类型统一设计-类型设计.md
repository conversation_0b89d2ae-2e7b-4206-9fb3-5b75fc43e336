# 媒体类型统一设计 - 类型设计（响应式重构版）

## 概述

当前项目中存在 `LocalMediaItem` 和 `AsyncProcessingMediaItem` 两套并行的媒体类型系统，导致架构复杂、代码重复、维护困难。本方案基于"核心数据与行为分离"的重构思路，提出**统一异步源**的设计，将"本地"和"异步"从**类型区分**改为**状态区分**，所有媒体项目都统一采用响应式异步处理模式。

### 核心理念：统一异步源 + 响应式架构

**所有媒体项目都是响应式异步源**，无论是用户选择的本地文件、工程加载的文件路径，还是远程下载的文件，都通过统一的响应式异步状态机进行处理：

- **本地文件**：瞬间完成获取阶段，但仍走完整的异步流程
- **远程文件**：正常的异步下载和处理流程
- **工程文件**：检查存在性后走异步流程
- **云盘文件**：同步后走异步流程

这种设计彻底消除了类型层面的复杂性，提供了真正统一的响应式架构。

## 当前问题分析

### 1. 概念模型混乱
```typescript
// ❌ 当前设计：两种类型
LocalMediaItem vs AsyncProcessingMediaItem

// 问题：本质上都是"媒体项目"，不应该在类型层面区分
```

### 2. 架构复杂度过高
- 两套并行的类型系统
- 两套状态管理API
- 两套UI渲染逻辑
- 复杂的类型守卫和转换逻辑

### 3. Vue3响应式问题
- 类实例属性的修改无法被Vue3响应式系统检测
- 依赖回调机制手动触发UI更新，容易遗漏且不可靠
- 组件中无法直接使用响应式特性

## 统一设计方案

### 1. 核心思路：统一异步源 + 响应式架构

**重新理解"状态"vs"类型"**：
- "本地"和"异步"是**状态**，不是**类型**
- **所有媒体都是响应式异步源**：统一为一种媒体类型，所有文件都走异步处理流程
- **差异体现在处理速度**：本地文件瞬间完成获取，远程文件需要下载时间
- **统一的状态转换**：所有媒体都经过相同的状态转换路径
- **响应式数据结构**：核心数据 + 行为函数 + 查询函数

**统一异步源 + 响应式的优势**：
- ✅ **架构完全统一**：消除所有类型层面的分支逻辑
- ✅ **完美响应式支持**：所有状态变化自动触发UI更新
- ✅ **代码高度复用**：一套代码处理所有媒体类型
- ✅ **扩展性极强**：新增数据源类型无需修改核心逻辑
- ✅ **UI逻辑统一**：所有媒体使用相同的状态显示组件
- ✅ **测试简化**：单一流程易于测试和维护

### 2. 双重状态设计思路

**核心洞察**：数据源状态和媒体处理状态是两个不同层面的概念
- **数据源状态**：负责"获取文件"（下载、同步、传输等）
- **媒体状态**：负责"处理文件"（解析、就绪、错误等）

**响应式设计优势**：
- 职责分离清晰：数据源管理获取逻辑，媒体项目管理处理逻辑
- 扩展性强：未来可支持云盘、FTP、数据库等多种数据源
- UI逻辑简化：UI只需关心抽象的媒体状态
- 完美响应式：所有状态变化自动触发UI更新

## 状态定义

### 1. 媒体处理状态（抽象层）

```typescript
type MediaStatus =
  | 'pending'         // 等待开始处理
  | 'asyncprocessing' // 异步获取中（抽象状态，对应各种数据源的获取阶段）
  | 'webavdecoding'   // WebAV解析中
  | 'ready'           // 就绪
  | 'error'           // 错误
  | 'cancelled'       // 取消
  | 'missing'         // 缺失（加载工程时本地文件不存在）
```

### 2. 数据源状态映射

数据源的内部状态会映射到媒体处理状态：

```typescript
// 数据源状态 → 媒体状态映射
UnifiedDataSourceData.status → UnifiedMediaItemData.mediaStatus
├── 'pending'     → 'pending'
├── 'acquiring'   → 'asyncprocessing' ✅ 关键映射
├── 'acquired'    → 'webavdecoding' (开始解析文件)
├── 'error'       → 'error'
└── 'cancelled'   → 'cancelled'

// 🔄 统一异步源：所有文件都走异步流程
// 本地文件：瞬间完成获取阶段
UserSelectedFileSourceData → 'acquiring'(瞬间完成) → 'acquired' → 'webavdecoding'
// 远程文件：正常异步获取
RemoteFileSourceData → 'acquiring'(下载过程) → 'acquired' → 'webavdecoding'
```

> **注意**：具体的数据源类型定义请参考 [数据源类型设计文档](./01-数据源基础类型设计.md)

## 统一媒体项目数据结构

### 1. 核心数据接口

```typescript
// 导入数据源类型定义
import type { UnifiedDataSourceData } from './01-数据源基础类型设计'

/**
 * 统一的媒体项目数据接口 - 纯响应式状态对象
 */
export interface UnifiedMediaItemData {
  // ==================== 核心属性 ====================
  readonly id: string
  name: string
  createdAt: string

  // ==================== 状态信息 ====================
  mediaStatus: MediaStatus
  mediaType: MediaType | 'unknown'

  // ==================== 数据源（包含获取状态） ====================
  source: UnifiedDataSourceData // 使用统一的响应式数据源

  // ==================== WebAV对象（状态相关） ====================
  webav?: WebAVObjects

  // ==================== 元数据（状态相关） ====================
  duration?: number // 媒体时长（帧数），可能在不同阶段获得：服务器提供、用户输入、WebAV解析等
}

/**
 * WebAV对象接口
 */
interface WebAVObjects {
  mp4Clip?: Raw<MP4Clip>
  imgClip?: Raw<ImgClip>
  audioClip?: Raw<AudioClip>
  thumbnailUrl?: string
  // WebAV解析得到的原始尺寸信息
  originalWidth?: number  // 原始宽度（视频和图片）
  originalHeight?: number // 原始高度（视频和图片）
}

/**
 * 工厂函数 - 创建响应式媒体项目数据
 */
export function createUnifiedMediaItemData(
  id: string,
  name: string,
  source: UnifiedDataSourceData,
  options?: Partial<UnifiedMediaItemData>
): UnifiedMediaItemData {
  return reactive({
    id,
    name,
    createdAt: new Date().toISOString(),
    mediaStatus: 'pending' as MediaStatus,
    mediaType: 'unknown' as MediaType | 'unknown',
    source,
    ...options
  })
}
```

### 2. 行为函数模块

```typescript
/**
 * 统一媒体项目行为函数 - 无状态操作函数
 */
export const UnifiedMediaItemActions = {
  // 状态转换
  transitionTo(
    item: UnifiedMediaItemData,
    newStatus: MediaStatus,
    context?: MediaTransitionContext
  ): boolean {
    if (!UnifiedMediaItemQueries.canTransitionTo(item, newStatus)) {
      console.warn(`无效状态转换: ${item.mediaStatus} → ${newStatus}`)
      return false
    }

    const oldStatus = item.mediaStatus
    item.mediaStatus = newStatus

    // 触发全局事件（替代回调机制）
    UnifiedMediaItemEvents.emit('statusChanged', {
      item,
      oldStatus,
      newStatus,
      context
    })

    return true
  },

  // 开始WebAV处理
  async startWebAVProcessing(item: UnifiedMediaItemData): Promise<void> {
    if (!UnifiedMediaItemActions.transitionTo(item, 'webavdecoding')) return

    try {
      // WebAV处理逻辑...
      const webavObjects = await processWithWebAV(item.source)
      item.webav = webavObjects
      item.duration = webavObjects.duration

      UnifiedMediaItemActions.transitionTo(item, 'ready')
    } catch (error) {
      UnifiedMediaItemActions.transitionTo(item, 'error', {
        type: 'error',
        errorMessage: error.message,
        errorCode: 'WEBAV_ERROR'
      })
    }
  },

  // 取消处理
  cancel(item: UnifiedMediaItemData): void {
    if (UnifiedMediaItemQueries.isProcessing(item)) {
      // 取消数据源任务
      if (item.source && DataSourceQueries.canCancel(item.source)) {
        UnifiedDataSourceActions.cancel(item.source)
      }
      UnifiedMediaItemActions.transitionTo(item, 'cancelled')
    }
  },

  // 重试处理
  retry(item: UnifiedMediaItemData): void {
    if (UnifiedMediaItemQueries.canRetry(item)) {
      UnifiedMediaItemActions.transitionTo(item, 'pending')
      // 重新开始处理流程...
    }
  }
}
```

### 3. 查询函数模块

```typescript
/**
 * 统一媒体项目查询函数 - 无状态查询函数
 */
export const UnifiedMediaItemQueries = {
  // 状态检查
  isPending(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'pending'
  },

  isReady(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'ready'
  },

  isProcessing(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'asyncprocessing' ||
           item.mediaStatus === 'webavdecoding'
  },

  hasError(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'error'
  },

  hasAnyError(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'error' ||
           item.mediaStatus === 'cancelled' ||
           item.mediaStatus === 'missing'
  },

  isParsing(item: UnifiedMediaItemData): boolean {
    return UnifiedMediaItemQueries.isPending(item) ||
           UnifiedMediaItemQueries.isProcessing(item)
  },

  // 状态转换验证
  canTransitionTo(item: UnifiedMediaItemData, newStatus: MediaStatus): boolean {
    const validTransitions: Record<MediaStatus, MediaStatus[]> = {
      pending: ['asyncprocessing', 'error', 'cancelled'],
      asyncprocessing: ['webavdecoding', 'error', 'cancelled'],
      webavdecoding: ['ready', 'error', 'cancelled'],
      ready: ['error'],
      error: ['pending', 'cancelled'],
      cancelled: ['pending'],
      missing: ['pending', 'cancelled'],
    }

    return validTransitions[item.mediaStatus]?.includes(newStatus) || false
  },

  // 业务查询
  canRetry(item: UnifiedMediaItemData): boolean {
    return item.mediaStatus === 'error' ||
           item.mediaStatus === 'cancelled' ||
           item.mediaStatus === 'missing'
  },

  getProgress(item: UnifiedMediaItemData): number | undefined {
    return DataSourceQueries.getProgress(item.source)
  },

  getError(item: UnifiedMediaItemData): string | undefined {
    return DataSourceQueries.getError(item.source)
  },

  getUrl(item: UnifiedMediaItemData): string | undefined {
    return DataSourceQueries.getUrl(item.source)
  },

  getDuration(item: UnifiedMediaItemData): number | undefined {
    return item.duration
  },

  getOriginalSize(item: UnifiedMediaItemData): { width: number; height: number } | undefined {
    const width = item.webav?.originalWidth
    const height = item.webav?.originalHeight
    return (width !== undefined && height !== undefined) ? { width, height } : undefined
  }
}
```

## 设计理念

### 1. 响应式优先原则

UnifiedMediaItemData采用**纯数据容器**和**无状态函数**的设计理念：

- **完美响应式**：所有状态变化自动触发UI更新
- **自动化处理**：创建后自动开始处理，无需手动调用方法
- **状态机保护**：通过查询函数验证状态转换的合法性
- **函数式设计**：所有操作都是无状态的纯函数
- **只读为主**：大部分函数是状态查询，而非状态修改

### 2. 保留的核心功能

#### 行为函数
- **transitionTo()**：状态转换的核心方法
- **cancel()**：用户可能需要取消正在处理的媒体
- **retry()**：错误状态下的重新尝试

#### 查询函数
- **状态查询**：`isReady()`, `isProcessing()`, `hasError()` 等
- **信息获取**：`getUrl()`, `getDuration()`, `getProgress()` 等

### 3. 移除的操作方法

以下方法在响应式架构下不再需要：

```typescript
// ❌ 已移除的方法
startProcessing()     // 创建时自动开始
pause() / resume()    // 由管理器控制
setStatus()          // 状态由行为函数管理
forceTransition()    // 违反状态机原则
onStatusChanged()    // 使用响应式监听替代
```

## 转换上下文设计

转换上下文（MediaTransitionContext）是状态转换时传递附加信息的机制，让状态机能够在保持简洁的同时支持复杂的业务场景。

### 1. 基础上下文结构

```typescript
// 基础上下文 - 所有转换都包含的通用信息
interface BaseMediaTransitionContext {
  timestamp: number         // 转换时间戳
  source: string           // 触发转换的来源
  reason: string           // 转换原因
  metadata?: Record<string, any> // 其他元数据
}
```

### 2. 具体上下文类型

```typescript
// 开始异步处理的上下文
interface AsyncProcessingContext extends BaseMediaTransitionContext {
  type: 'async_processing'
  initialProgress?: number  // 初始进度
  estimatedDuration?: number // 预估总耗时
}

// WebAV解析完成上下文
interface ParseCompletedContext extends BaseMediaTransitionContext {
  type: 'parse_completed'
  parsedMetadata: {         // 解析得到的元数据
    duration: number
    resolution?: string
    format: string
    bitrate?: number
    codecInfo?: {
      video?: string
      audio?: string
    }
  }
  thumbnailGenerated: boolean // 是否生成了缩略图
  webavObjects: WebAVObjects  // WebAV解析结果
  parseTime: number          // 解析耗时(ms)
}

// 错误转换上下文
interface ErrorContext extends BaseMediaTransitionContext {
  type: 'error'
  errorMessage: string      // 错误描述
  errorCode: string         // 错误代码
  errorDetails?: any        // 详细错误信息
  retryable: boolean        // 是否可重试
  retryCount?: number       // 重试次数
  lastAttemptTime?: number  // 上次尝试时间
}

// 取消转换上下文
interface CancelledContext extends BaseMediaTransitionContext {
  type: 'cancelled'
  cancelReason: string      // 取消原因
  partialProgress?: number  // 取消时的进度
  canResume?: boolean       // 是否可恢复
}

// 重试转换上下文
interface RetryContext extends BaseMediaTransitionContext {
  type: 'retry'
  retryCount: number        // 重试次数
  previousError?: string    // 上次错误信息
  retryDelay?: number       // 重试延迟(ms)
}

// 文件缺失上下文
interface MissingContext extends BaseMediaTransitionContext {
  type: 'missing'
  originalPath?: string     // 原始文件路径
  lastModified?: number     // 文件最后修改时间
  expectedSize?: number     // 预期文件大小
}

// 联合类型定义
type MediaTransitionContext =
  | AsyncProcessingContext
  | ParseCompletedContext
  | ErrorContext
  | CancelledContext
  | RetryContext
  | MissingContext
```

### 3. 联合类型设计优势

1. **类型安全**
   - 每种转换场景都有明确的上下文结构
   - TypeScript 可以提供精确的类型检查和代码提示
   - 避免传递不相关的上下文信息

2. **语义清晰**
   - 通过 `type` 字段明确标识上下文类型
   - 不同场景的上下文字段有明确的业务含义
   - 便于理解和维护

3. **扩展性强**
   - 新增转换场景时只需添加新的上下文类型
   - 不影响现有的上下文结构
   - 支持渐进式功能增强

4. **可调试性好**
   - 每个上下文都包含完整的转换信息
   - 便于日志记录和问题排查
   - 支持详细的状态转换追踪

## 状态转换规则

```typescript
/**
 * 合法的状态转换映射表
 */
const VALID_MEDIA_TRANSITIONS: Record<MediaStatus, MediaStatus[]> = {
  'pending': ['asyncprocessing', 'webavdecoding', 'error', 'missing'],
  'asyncprocessing': ['webavdecoding', 'error', 'cancelled'],
  'webavdecoding': ['ready', 'error'],
  'ready': ['error'], // 运行时可能出错
  'error': ['pending'], // 支持重试
  'cancelled': ['pending'], // 支持重新开始
  'missing': ['pending', 'error'] // 重新选择文件或确认错误
}

/**
 * 状态转换实现（在查询函数中）
 */
export const UnifiedMediaItemQueries = {
  // ... 其他查询函数

  canTransitionTo(item: UnifiedMediaItemData, newStatus: MediaStatus): boolean {
    return VALID_MEDIA_TRANSITIONS[item.mediaStatus]?.includes(newStatus) ?? false
  }
}

/**
 * 状态转换实现（在行为函数中）
 */
export const UnifiedMediaItemActions = {
  // ... 其他行为函数

  transitionTo(
    item: UnifiedMediaItemData,
    newStatus: MediaStatus,
    context?: MediaTransitionContext
  ): boolean {
    if (!UnifiedMediaItemQueries.canTransitionTo(item, newStatus)) {
      console.warn(`无效状态转换: ${item.mediaStatus} → ${newStatus}`)
      return false
    }

    const oldStatus = item.mediaStatus
    item.mediaStatus = newStatus

    // 触发全局事件（替代回调机制）
    UnifiedMediaItemEvents.emit('statusChanged', {
      item,
      oldStatus,
      newStatus,
      context
    })

    return true
  }
}
```

## 各状态字段约束

### 1. pending状态
```typescript
{
  id: string ✅
  name: string ✅
  mediaType: 'unknown' ✅ // 还未检测
  source: UnifiedDataSourceData ✅ // 任何响应式数据源类型
  source.status: 'pending' ✅
  webav: undefined ❌
  duration: undefined | number // 可能有用户预输入的预期时长
}
```

### 2. asyncprocessing状态（统一异步源 - 所有数据源都经过此状态）
```typescript
{
  id: string ✅
  name: string ✅
  mediaType: 'unknown' ✅ // 仍未检测到实际类型
  source: UnifiedDataSourceData ✅ // 数据源正在获取中
  source.status: 'acquiring' ✅
  source.progress: 0-99 ✅
  webav: undefined ❌
  duration: undefined | number // 可能有服务器提供的时长或用户预输入的时长
}
```

### 3. webavdecoding状态
```typescript
{
  id: string ✅
  name: string ✅
  mediaType: 'unknown' ✅ // 正在检测中
  source: UnifiedDataSourceData ✅ // 数据源已获取完成
  source.status: 'acquired' ✅
  webav: undefined ❌ // 或部分创建
  duration: undefined | number // 可能有服务器/用户提供的时长，WebAV解析会确认或更新
}
```

### 4. ready状态
```typescript
{
  id: string ✅
  name: string ✅
  mediaType: MediaType ✅ // 已检测到确切类型
  source: UnifiedDataSourceData ✅ // 数据源已获取完成
  source.status: 'acquired' ✅
  webav: {
    mp4Clip?: Raw<MP4Clip> ✅ // 根据类型
    imgClip?: Raw<ImgClip> ✅
    audioClip?: Raw<AudioClip> ✅
    thumbnailUrl?: string ✅
    originalWidth?: number ✅  // WebAV解析的原始宽度
    originalHeight?: number ✅ // WebAV解析的原始高度
  }
  duration: number ✅ // WebAV解析确认的最终准确值
}
```

### 5. error/cancelled状态
```typescript
{
  id: string ✅
  name: string ✅
  mediaType: 'unknown' | MediaType ✅ // 可能已检测或未检测
  source: UnifiedDataSourceData ✅
  source.status: 'error' | 'cancelled' ✅
  source.errorMessage: string ✅ // 错误信息
  webav: undefined ❌
  duration: undefined | number // 可能有服务器/用户提供的预估值，保留用于重试
}
```

### 6. missing状态（项目加载时）
```typescript
{
  id: string ✅
  name: string ✅
  mediaType: MediaType ✅ // 从项目配置中已知
  source: UnifiedDataSourceData ✅ // 数据源可能无效
  webav: undefined ❌
  duration: number ✅ // 从项目配置恢复
}
```

## 状态机设计原则

### 响应式数据流管理

所有状态变更和数据设置都必须通过行为函数进行，确保：

1. **完整的状态追踪**：每次变更都有完整的转换上下文记录
2. **类型安全**：利用 TypeScript 的联合类型确保上下文类型安全
3. **响应式更新**：所有状态变化自动触发UI更新
4. **可追溯性**：通过事件系统可以完整追踪所有状态变更

### 已移除的方法

以下方法已从设计中移除，改为通过响应式数据和行为函数处理：

- ~~`setUserDuration(duration, description)`~~ → 直接修改 `item.duration`
- ~~`setServerMetadata(metadata)`~~ → 通过转换上下文传递
- ~~`onStatusChanged(callback)`~~ → 使用Vue的 `watch` 或 `watchEffect`

### 正确的使用方式

```typescript
// ❌ 错误：直接修改状态（已移除）
// mediaItem.setUserDuration(1800, "用户预估")
// mediaItem.setServerMetadata({ duration: 3600 })

// ✅ 正确：通过行为函数和响应式数据
// 1. 直接修改响应式数据
item.duration = 1800

// 2. 通过行为函数进行状态转换
UnifiedMediaItemActions.transitionTo(item, 'ready', {
  type: 'parse_completed',
  timestamp: Date.now(),
  source: 'webav',
  reason: 'WebAV parsing completed',
  parsedMetadata: {
    duration: 3600,
    format: 'mp4'
  }
})

// 3. 使用Vue响应式监听状态变化
watch(
  () => item.mediaStatus,
  (newStatus, oldStatus) => {
    console.log(`状态变化: ${oldStatus} → ${newStatus}`)
  }
)
```

## Duration多来源设计

### Duration的获取时机和来源

媒体项目的duration字段采用**多来源、渐进式精确**的响应式设计，可以在不同阶段从不同来源获得：

#### 1. 用户预输入（pending/asyncprocessing阶段）
```typescript
// 用户在添加远程文件时预先输入预期时长
// 直接修改响应式数据
item.duration = 1800 // 1800帧 = 30秒@60fps

// 可选：记录来源信息
UnifiedMediaItemActions.transitionTo(item, item.mediaStatus, {
  type: 'async_processing',
  timestamp: Date.now(),
  source: 'user',
  reason: 'User provided duration estimate'
})
```

#### 2. 服务器元数据（asyncprocessing阶段）
```typescript
// 远程下载时，服务器HTTP头或API响应提供的元数据
// 直接更新响应式数据
item.duration = 3600 // 服务器提供的时长

// 记录转换上下文
UnifiedMediaItemActions.transitionTo(item, 'asyncprocessing', {
  type: 'async_processing',
  timestamp: Date.now(),
  source: 'server',
  reason: 'Server provided metadata'
})
```

#### 3. WebAV解析（ready阶段）
```typescript
// WebAV解析文件后得到的精确时长
// 通过行为函数更新
UnifiedMediaItemActions.transitionTo(item, 'ready', {
  type: 'parse_completed',
  timestamp: Date.now(),
  source: 'webav',
  reason: 'WebAV parsing completed',
  parsedMetadata: {
    duration: 3598, // WebAV解析的精确时长
    format: 'mp4'
  }
})

// 在行为函数内部会更新 item.duration = context.parsedMetadata.duration
```

### Duration优先级和更新策略

1. **初始值**：用户预输入 > undefined
2. **异步处理阶段**：服务器提供 > 用户预输入 > undefined
3. **最终值**：WebAV解析 > 服务器提供 > 用户预输入

### 响应式监听示例

```vue
<template>
  <div>
    <!-- ✅ 响应式显示时长 -->
    <p v-if="item.duration">时长: {{ formatDuration(item.duration) }}</p>
    <p v-else>时长未知</p>
  </div>
</template>

<script setup>
// ✅ 自动响应时长变化
watch(
  () => item.duration,
  (newDuration, oldDuration) => {
    if (newDuration !== oldDuration) {
      console.log(`时长更新: ${oldDuration} → ${newDuration}`)
    }
  }
)
</script>
```

### 使用场景

- **远程下载**：用户预输入 → 服务器确认 → WebAV精确解析
- **本地文件**：undefined → WebAV解析（瞬间完成）
- **项目加载**：项目配置恢复 → WebAV重新验证
- **文本媒体**：用户设置 → 直接就绪

## 原始尺寸信息设计

### 尺寸信息的获取时机和来源

媒体项目的原始尺寸信息（originalWidth/originalHeight）采用**多来源、渐进式精确**的响应式设计：

#### 1. 服务器提供（asyncprocessing阶段）
```typescript
// 远程下载时，服务器API响应提供的尺寸信息
// 直接更新响应式数据
if (!item.webav) {
  item.webav = {}
}
item.webav.originalWidth = 1920
item.webav.originalHeight = 1080

// 记录转换上下文
UnifiedMediaItemActions.transitionTo(item, 'asyncprocessing', {
  type: 'async_processing',
  timestamp: Date.now(),
  source: 'server',
  reason: 'Server provided metadata'
})
```

#### 2. WebAV解析（ready阶段）
```typescript
// WebAV解析文件后得到的精确尺寸
UnifiedMediaItemActions.transitionTo(item, 'ready', {
  type: 'parse_completed',
  timestamp: Date.now(),
  source: 'webav',
  reason: 'WebAV parsing completed',
  parsedMetadata: {
    originalWidth: 1920,  // WebAV解析的精确宽度
    originalHeight: 1080, // WebAV解析的精确高度
    duration: 3598,
    format: 'mp4'
  }
})

// 在行为函数内部会更新 item.webav.originalWidth/originalHeight
```

### 尺寸信息的使用方法

```typescript
// 使用查询函数获取尺寸信息
const size = UnifiedMediaItemQueries.getOriginalSize(item) // { width: number; height: number } | undefined

// 在Vue组件中响应式使用
const hasSize = computed(() => {
  return item.webav?.originalWidth !== undefined &&
         item.webav?.originalHeight !== undefined
})
```

### Vue组件中的响应式使用

```vue
<template>
  <div>
    <!-- ✅ 响应式显示尺寸信息 -->
    <div v-if="originalSize" class="size-info">
      尺寸: {{ originalSize.width }} x {{ originalSize.height }}
    </div>
    <div v-else class="no-size">
      尺寸信息未知
    </div>
  </div>
</template>

<script setup>
// ✅ 响应式计算属性
const originalSize = computed(() =>
  UnifiedMediaItemQueries.getOriginalSize(item)
)

// ✅ 监听尺寸变化
watch(originalSize, (newSize, oldSize) => {
  if (newSize && !oldSize) {
    console.log(`获得尺寸信息: ${newSize.width}x${newSize.height}`)
  }
})
</script>
```

### 适用的媒体类型

- **视频媒体**：必须有原始尺寸信息
- **图片媒体**：必须有原始尺寸信息
- **音频媒体**：不需要尺寸信息
- **文本媒体**：不需要尺寸信息（使用样式配置的尺寸）

## 设计优势总结

### 1. 统一异步源 + 响应式架构
- **完全统一**：所有媒体项目都是响应式异步源，消除类型分支
- **完美响应式**：所有状态变化自动触发UI更新
- **数据源层**：专注文件获取（下载、同步、传输、验证）
- **媒体层**：专注文件处理（解析、就绪、错误）
- **职责分离**：各层管理自己的状态和逻辑
- **处理差异化**：通过获取速度区分，而非类型区分

### 2. 扩展性强
- 数据源类型可独立扩展，无需修改媒体类型核心逻辑
- 新的数据源只需实现统一的状态映射接口
- 支持云盘、FTP、数据库等多种数据源类型
- 函数式设计便于测试和维护

### 3. 开发体验优秀
- 类型安全的查询函数和行为函数
- Vue3响应式系统的完美支持
- 简化的状态管理和UI更新逻辑
- 更好的调试和开发工具支持

> 详细的数据源扩展设计请参考 [数据源类型设计文档](./01-数据源基础类型设计.md)
