# 数据源管理器并发控制重构方案

## 概述

当前数据源管理器的并发控制存在以下问题：
1. `processQueue()` 调用散落在各个地方，难以管理
2. 缺乏统一的任务调度策略
3. 不同类型数据源使用相同的并发策略
4. 缺乏全局资源协调和优化
5. 监控和可观测性不足

本方案提出基于**三层架构 + 事件驱动**的重构设计，彻底解决当前架构问题。

## 核心设计理念

### 架构分层
- **任务调度层**：专门负责并发控制、队列管理、优先级调度
- **业务执行层**：专门负责具体的数据源获取逻辑  
- **状态管理层**：统一的状态变更和通知机制

### 设计原则
1. **单一职责**：每个组件只负责一个明确的功能
2. **事件驱动**：消除轮询和散落的调用
3. **配置驱动**：不同类型任务的策略可配置
4. **资源优化**：全局资源池统一分配
5. **可观测性**：内置完善的监控能力

## 详细设计方案

### 1. 全局任务调度器（GlobalTaskScheduler）

```typescript
/**
 * 全局任务调度器 - 系统核心组件
 * 负责统一的任务调度、资源分配和并发控制
 */
class GlobalTaskScheduler {
  private static instance: GlobalTaskScheduler
  
  // 核心组件
  private queues: Map<string, PriorityQueue<ScheduledTask>> = new Map()
  private executors: Map<string, TaskExecutor> = new Map()
  private resourcePool: ResourcePool
  private eventBus: EventBus
  private monitor: TaskMonitor
  private strategy: SchedulingStrategy
  
  // 任务提交接口
  submitTask(task: ScheduledTask, executorType: string): void {
    // 添加到对应类型的优先级队列
    // 触发调度事件
  }
  
  // 核心调度逻辑 - 事件驱动，避免轮询
  private scheduleNext(): void {
    // 基于优先级和资源可用性选择下一个任务
    // 分配资源并启动执行
    // 注册完成回调
  }
  
  // 资源释放回调
  private onTaskComplete(taskId: string, executorType: string): void {
    // 释放资源
    // 触发下一轮调度
    // 更新监控指标
  }
}
```

### 2. 资源池管理（ResourcePool）

```typescript
/**
 * 资源配置接口
 */
interface ResourceConfig {
  maxConcurrent: number    // 最大并发数
  priority: number         // 基础优先级
  resourceWeight: number   // 资源权重（CPU/网络/内存）
  adaptiveScaling: boolean // 是否支持自适应扩缩容
}

/**
 * 统一资源池 - 负责全局资源分配和优化
 */
class ResourcePool {
  private configs: Map<string, ResourceConfig> = new Map([
    ['user-selected', { 
      maxConcurrent: 10, 
      priority: 1, 
      resourceWeight: 0.1,
      adaptiveScaling: true 
    }],
    ['remote', { 
      maxConcurrent: 3, 
      priority: 2, 
      resourceWeight: 1.0,
      adaptiveScaling: false 
    }],
    ['cloud', { 
      maxConcurrent: 2, 
      priority: 3, 
      resourceWeight: 0.5,
      adaptiveScaling: true 
    }]
  ])
  
  private currentUsage: Map<string, number> = new Map()
  private systemLoad: SystemLoadMonitor
  
  // 资源分配决策
  allocateResource(executorType: string): boolean {
    const config = this.configs.get(executorType)
    const current = this.currentUsage.get(executorType) || 0
    
    // 基于配置、当前使用量和系统负载决策
    return this.canAllocate(config, current)
  }
  
  // 动态资源调整
  private adjustResources(): void {
    // 基于系统负载和任务队列长度动态调整并发数
    // 支持自适应扩缩容的执行器类型
  }
}
```

### 3. 事件驱动状态管理

```typescript
/**
 * 统一任务事件类型
 */
type TaskEvent = 
  | { type: 'TASK_ADDED', taskId: string, executorType: string, priority: number }
  | { type: 'TASK_STARTED', taskId: string, executorType: string }
  | { type: 'TASK_PROGRESS', taskId: string, progress: number }
  | { type: 'TASK_COMPLETED', taskId: string, executorType: string, duration: number }
  | { type: 'TASK_FAILED', taskId: string, executorType: string, error: Error }
  | { type: 'TASK_CANCELLED', taskId: string, executorType: string }
  | { type: 'RESOURCE_AVAILABLE', executorType: string }
  | { type: 'SYSTEM_LOAD_CHANGED', loadLevel: 'low' | 'medium' | 'high' }

/**
 * 事件总线 - 统一的事件分发机制
 */
class EventBus {
  private listeners: Map<string, Function[]> = new Map()
  private eventQueue: TaskEvent[] = []
  private processing = false
  
  // 异步事件发射，避免阻塞
  emit(event: TaskEvent): void {
    this.eventQueue.push(event)
    
    if (!this.processing) {
      this.processing = true
      // 使用微任务确保事件处理不阻塞主线程
      Promise.resolve().then(() => this.processEventQueue())
    }
  }
  
  // 批量事件处理
  private async processEventQueue(): void {
    while (this.eventQueue.length > 0) {
      const event = this.eventQueue.shift()!
      await this.notifyListeners(event)
    }
    this.processing = false
  }
  
  // 支持事件中间件
  use(middleware: EventMiddleware): void {
    // 支持日志、监控、调试等中间件
  }
}
```

### 4. 重构后的管理器基类

```typescript
/**
 * 简化的数据源管理器基类
 * 只负责任务提交和业务逻辑执行，不再处理并发控制
 */
export abstract class DataSourceManager<T extends UnifiedDataSourceData> {
  protected abstract executorType: string
  private scheduler = GlobalTaskScheduler.getInstance()
  private monitor = TaskMonitor.getInstance()
  
  // ==================== 简化的公共接口 ====================
  
  /**
   * 开始获取任务 - 只负责任务提交
   */
  startAcquisition(source: T, taskId: string): void {
    const task = this.createScheduledTask(source, taskId)
    
    // 提交给全局调度器，不再直接处理
    this.scheduler.submitTask(task, this.executorType)
    
    // 记录监控指标
    this.monitor.recordTaskSubmission(taskId, this.executorType)
  }
  
  /**
   * 取消任务
   */
  cancelTask(taskId: string): boolean {
    return this.scheduler.cancelTask(taskId, this.executorType)
  }
  
  /**
   * 重试任务
   */
  retryTask(taskId: string): boolean {
    return this.scheduler.retryTask(taskId, this.executorType)
  }
  
  /**
   * 获取统计信息 - 从全局监控器获取
   */
  getStats(): ManagerStats {
    return this.monitor.getExecutorStats(this.executorType)
  }
  
  // ==================== 抽象方法 ====================
  
  /**
   * 执行具体任务 - 子类只需实现业务逻辑
   */
  abstract executeTask(task: AcquisitionTask<T>): Promise<void>
  
  /**
   * 获取资源配置 - 子类定义自己的资源需求
   */
  abstract getResourceConfig(): ResourceConfig
  
  /**
   * 获取管理器类型
   */
  abstract getManagerType(): string
  
  // ==================== 私有方法 ====================
  
  private createScheduledTask(source: T, taskId: string): ScheduledTask {
    return {
      id: taskId,
      source,
      executorType: this.executorType,
      priority: this.calculatePriority(source),
      createdAt: Date.now(),
      retryCount: 0
    }
  }
  
  private calculatePriority(source: T): number {
    // 基于数据源类型和用户交互情况计算优先级
    const baseConfig = this.getResourceConfig()
    const userInteraction = this.isUserInitiated(source) ? 10 : 0
    return baseConfig.priority + userInteraction
  }
}
```

### 5. 具体管理器实现示例

```typescript
/**
 * 用户选择文件管理器 - 重构后实现
 */
class UserSelectedFileManager extends DataSourceManager<UserSelectedFileSourceData> {
  protected executorType = 'user-selected'

  /**
   * 执行任务 - 只关注业务逻辑，不管并发控制
   */
  async executeTask(task: AcquisitionTask<UserSelectedFileSourceData>): Promise<void> {
    try {
      // 直接调用数据源行为函数
      await UserSelectedFileActions.executeAcquisition(task.source)
    } catch (error) {
      // 统一的错误处理
      throw new TaskExecutionError(
        `用户文件处理失败: ${error.message}`,
        'USER_FILE_ERROR',
        error
      )
    }
  }

  /**
   * 资源配置 - 文件验证快，可以高并发
   */
  getResourceConfig(): ResourceConfig {
    return {
      maxConcurrent: 10,        // 文件验证快，支持高并发
      priority: 1,              // 用户操作优先级最高
      resourceWeight: 0.1,      // CPU密集但耗时短
      adaptiveScaling: true     // 支持自适应调整
    }
  }

  getManagerType(): string {
    return 'user-selected-file'
  }
}

/**
 * 远程文件管理器 - 重构后实现
 */
class RemoteFileManager extends DataSourceManager<RemoteFileSourceData> {
  protected executorType = 'remote'

  /**
   * 执行任务 - 网络下载逻辑
   */
  async executeTask(task: AcquisitionTask<RemoteFileSourceData>): Promise<void> {
    try {
      await RemoteFileActions.executeAcquisition(task.source)
    } catch (error) {
      // 根据错误类型决定是否可重试
      const retryable = this.isRetryableError(error)
      throw new TaskExecutionError(
        `远程文件下载失败: ${error.message}`,
        retryable ? 'NETWORK_ERROR' : 'PERMANENT_ERROR',
        error
      )
    }
  }

  /**
   * 资源配置 - 网络下载，限制并发
   */
  getResourceConfig(): ResourceConfig {
    return {
      maxConcurrent: 3,         // 网络下载，限制并发避免带宽竞争
      priority: 2,              // 中等优先级
      resourceWeight: 1.0,      // 网络IO密集
      adaptiveScaling: false    // 固定并发数，避免网络拥塞
    }
  }

  getManagerType(): string {
    return 'remote-file'
  }

  private isRetryableError(error: any): boolean {
    // 网络错误、超时等可重试，格式错误等不可重试
    return error.code === 'NETWORK_ERROR' ||
           error.code === 'TIMEOUT' ||
           error.code === 'SERVER_ERROR'
  }
}

/**
 * 云盘文件管理器 - 未来扩展示例
 */
class CloudFileManager extends DataSourceManager<CloudFileSourceData> {
  protected executorType = 'cloud'

  async executeTask(task: AcquisitionTask<CloudFileSourceData>): Promise<void> {
    try {
      await CloudFileActions.executeAcquisition(task.source)
    } catch (error) {
      throw new TaskExecutionError(
        `云盘文件同步失败: ${error.message}`,
        this.getErrorCode(error),
        error
      )
    }
  }

  getResourceConfig(): ResourceConfig {
    return {
      maxConcurrent: 2,         // API限制，低并发
      priority: 3,              // 较低优先级
      resourceWeight: 0.5,      // 中等资源消耗
      adaptiveScaling: true     // 根据API限制动态调整
    }
  }

  getManagerType(): string {
    return 'cloud-file'
  }
}
```

### 6. 智能调度策略

```typescript
/**
 * 调度任务接口
 */
interface ScheduledTask {
  id: string
  source: UnifiedDataSourceData
  executorType: string
  priority: number
  createdAt: number
  retryCount: number
  estimatedDuration?: number
}

/**
 * 智能调度策略 - 多因素任务选择
 */
class SchedulingStrategy {
  private historyData: TaskHistoryData

  /**
   * 选择下一个要执行的任务
   * 综合考虑：优先级、等待时间、资源权重、预估执行时间
   */
  selectNextTask(availableTasks: ScheduledTask[]): ScheduledTask | null {
    if (availableTasks.length === 0) return null

    return availableTasks
      .filter(task => this.canExecute(task))
      .sort((a, b) => {
        const scoreA = this.calculateScore(a)
        const scoreB = this.calculateScore(b)
        return scoreB - scoreA  // 分数高的优先
      })[0] || null
  }

  /**
   * 计算任务综合分数
   */
  private calculateScore(task: ScheduledTask): number {
    const waitTime = Date.now() - task.createdAt
    const priority = task.priority
    const resourceAvailability = this.getResourceAvailability(task.executorType)
    const starvationPenalty = this.getStarvationPenalty(task)

    // 综合评分公式
    return (
      priority * 100 +                    // 基础优先级权重最高
      Math.min(waitTime * 0.01, 50) +     // 等待时间，最多50分
      resourceAvailability * 30 +         // 资源可用性
      starvationPenalty                   // 防饥饿惩罚
    )
  }

  /**
   * 防止任务饥饿的惩罚分数
   */
  private getStarvationPenalty(task: ScheduledTask): number {
    const waitTime = Date.now() - task.createdAt
    const threshold = 30000 // 30秒阈值

    if (waitTime > threshold) {
      // 等待时间超过阈值，给予额外分数防止饥饿
      return Math.min((waitTime - threshold) * 0.001, 100)
    }

    return 0
  }

  /**
   * 获取资源可用性评分
   */
  private getResourceAvailability(executorType: string): number {
    const pool = ResourcePool.getInstance()
    const usage = pool.getCurrentUsage(executorType)
    const max = pool.getMaxConcurrent(executorType)

    // 可用性越高，分数越高
    return ((max - usage) / max) * 100
  }

  /**
   * 基于历史数据预测任务执行时间
   */
  predictExecutionTime(task: ScheduledTask): number {
    return this.historyData.getAverageExecutionTime(
      task.executorType,
      task.source.type
    )
  }
}
```

### 7. 监控和可观测性

```typescript
/**
 * 执行器指标接口
 */
interface ExecutorMetrics {
  totalSubmitted: number
  totalCompleted: number
  totalFailed: number
  currentPending: number
  currentRunning: number
  executionTimes: number[]
}

/**
 * 任务监控器 - 全面的性能监控和统计
 */
class TaskMonitor {
  private static instance: TaskMonitor
  private metrics: Map<string, ExecutorMetrics> = new Map()
  private systemMetrics: SystemMetrics
  private eventHistory: TaskEvent[] = []
  private taskStartTimes: Map<string, number> = new Map()

  /**
   * 记录任务提交
   */
  recordTaskSubmission(taskId: string, executorType: string): void {
    const metrics = this.getOrCreateMetrics(executorType)
    metrics.totalSubmitted++
    metrics.currentPending++

    this.emitMetricUpdate(executorType, 'task_submitted')
  }

  /**
   * 记录任务开始执行
   */
  recordTaskStart(taskId: string, executorType: string): void {
    const metrics = this.getOrCreateMetrics(executorType)
    metrics.currentPending--
    metrics.currentRunning++

    this.taskStartTimes.set(taskId, Date.now())
    this.emitMetricUpdate(executorType, 'task_started')
  }

  /**
   * 记录任务完成
   */
  recordTaskComplete(taskId: string, executorType: string): void {
    const startTime = this.taskStartTimes.get(taskId)
    if (startTime) {
      const duration = Date.now() - startTime
      this.recordExecutionTime(executorType, duration)
      this.taskStartTimes.delete(taskId)
    }

    const metrics = this.getOrCreateMetrics(executorType)
    metrics.currentRunning--
    metrics.totalCompleted++

    this.emitMetricUpdate(executorType, 'task_completed')
  }

  /**
   * 获取执行器统计信息
   */
  getExecutorStats(executorType: string): ExecutorStats {
    const metrics = this.metrics.get(executorType)
    if (!metrics) return this.createEmptyStats()

    return {
      totalSubmitted: metrics.totalSubmitted,
      totalCompleted: metrics.totalCompleted,
      totalFailed: metrics.totalFailed,
      currentPending: metrics.currentPending,
      currentRunning: metrics.currentRunning,
      averageExecutionTime: this.calculateAverageTime(executorType),
      successRate: this.calculateSuccessRate(executorType),
      throughput: this.calculateThroughput(executorType)
    }
  }

  /**
   * 获取系统整体统计
   */
  getSystemStats(): SystemStats {
    const allExecutors = Array.from(this.metrics.keys())

    return {
      totalExecutors: allExecutors.length,
      totalTasks: this.getTotalTasks(),
      systemLoad: this.systemMetrics.getCurrentLoad(),
      memoryUsage: this.systemMetrics.getMemoryUsage(),
      averageWaitTime: this.calculateSystemAverageWaitTime(),
      resourceUtilization: this.calculateResourceUtilization()
    }
  }

  /**
   * 实时性能监控
   */
  startRealTimeMonitoring(): void {
    setInterval(() => {
      this.updateSystemMetrics()
      this.detectPerformanceIssues()
      this.emitSystemMetricsUpdate()
    }, 5000) // 每5秒更新一次
  }

  /**
   * 性能问题检测
   */
  private detectPerformanceIssues(): void {
    // 检测队列积压
    this.detectQueueBacklog()

    // 检测执行时间异常
    this.detectExecutionTimeAnomalies()

    // 检测资源利用率异常
    this.detectResourceUtilizationIssues()

    // 检测错误率异常
    this.detectErrorRateSpikes()
  }
}
```

## 迁移策略

### 阶段1：基础设施搭建（1-2周）

#### 1.1 核心组件创建
- 创建 `GlobalTaskScheduler` 单例类
- 实现 `ResourcePool` 资源管理
- 构建 `EventBus` 事件系统
- 添加 `TaskMonitor` 监控组件

#### 1.2 并行运行准备
- 保持现有管理器不变
- 新旧系统并行运行
- 添加切换开关，支持A/B测试

#### 1.3 监控系统搭建
- 实现基础监控指标收集
- 添加性能对比工具
- 建立日志和调试系统

### 阶段2：渐进式迁移（2-3周）

#### 2.1 优先迁移用户选择文件管理器
- 风险最低，逻辑最简单
- 验证新架构的稳定性
- 收集性能数据和用户反馈

#### 2.2 迁移远程文件管理器
- 验证网络IO密集型任务的处理
- 测试并发控制和资源分配
- 优化调度策略

#### 2.3 扩展支持其他管理器
- 根据需要迁移其他管理器类型
- 验证扩展性和配置灵活性

### 阶段3：优化和清理（1周）

#### 3.1 性能调优
- 基于监控数据优化调度策略
- 调整资源配置参数
- 优化事件处理性能

#### 3.2 代码清理
- 移除旧的并发控制代码
- 清理 `processQueue()` 相关调用
- 统一错误处理和日志

#### 3.3 文档和测试
- 更新API文档
- 补充单元测试和集成测试
- 编写运维和故障排查指南

## 核心优势总结

### 1. 架构优势

#### 职责分离清晰
- **调度器**：专注任务调度和资源分配
- **管理器**：专注业务逻辑执行
- **资源池**：专注资源管理和优化
- **监控器**：专注性能监控和问题诊断

#### 事件驱动架构
- 消除 `processQueue()` 的散落调用
- 异步处理，避免阻塞主线程
- 易于扩展和调试
- 支持中间件和插件

### 2. 功能优势

#### 智能调度
- 多因素综合评分算法
- 防饥饿机制
- 基于历史数据的预测
- 动态优先级调整

#### 差异化资源管理
- 不同类型任务的专门优化
- 自适应扩缩容支持
- 全局资源协调
- 系统负载感知

#### 全面监控
- 实时性能指标
- 问题自动检测
- 历史数据分析
- 可视化调试支持

### 3. 开发优势

#### 配置驱动
- 运行时动态调整
- 支持A/B测试
- 便于性能调优
- 环境特定配置

#### 扩展性强
- 新增数据源类型简单
- 调度策略可插拔
- 监控指标可扩展
- 支持未来需求变化

#### 可维护性高
- 代码结构清晰
- 职责边界明确
- 易于单元测试
- 便于问题定位

## 风险评估和应对

### 1. 技术风险

#### 复杂性增加
- **风险**：新架构增加了系统复杂性
- **应对**：分阶段实施，充分测试，完善文档

#### 性能开销
- **风险**：事件系统和监控可能带来性能开销
- **应对**：性能基准测试，优化关键路径，支持监控开关

### 2. 实施风险

#### 迁移成本
- **风险**：重构工作量较大
- **应对**：渐进式迁移，保持向后兼容，并行运行

#### 稳定性风险
- **风险**：新系统可能存在未知问题
- **应对**：充分测试，灰度发布，快速回滚机制

### 3. 业务风险

#### 用户体验影响
- **风险**：重构期间可能影响用户体验
- **应对**：A/B测试，用户反馈收集，快速响应机制

## 总结

这个重构方案通过**三层架构 + 事件驱动**的设计，彻底解决了当前并发控制的核心问题：

1. ✅ **消除散落调用**：统一的事件驱动调度
2. ✅ **职责分离**：调度、执行、监控各司其职
3. ✅ **差异化策略**：不同类型任务的专门优化
4. ✅ **全局协调**：统一的资源池和调度器
5. ✅ **可观测性**：完善的监控和调试能力
6. ✅ **扩展性**：易于添加新的数据源类型
7. ✅ **可维护性**：清晰的代码结构和边界

通过分阶段的实施策略，可以在保证系统稳定性的前提下，逐步完成架构升级，最终获得一个更加健壮、高效、可维护的并发控制系统。

### 7. 监控和可观测性

```typescript
/**
 * 执行器指标接口
 */
interface ExecutorMetrics {
  totalSubmitted: number
  totalCompleted: number
  totalFailed: number
  currentPending: number
  currentRunning: number
  executionTimes: number[]
}

/**
 * 任务监控器 - 全面的性能监控和统计
 */
class TaskMonitor {
  private static instance: TaskMonitor
  private metrics: Map<string, ExecutorMetrics> = new Map()
  private systemMetrics: SystemMetrics
  private eventHistory: TaskEvent[] = []
  private taskStartTimes: Map<string, number> = new Map()

  /**
   * 记录任务提交
   */
  recordTaskSubmission(taskId: string, executorType: string): void {
    const metrics = this.getOrCreateMetrics(executorType)
    metrics.totalSubmitted++
    metrics.currentPending++

    this.emitMetricUpdate(executorType, 'task_submitted')
  }

  /**
   * 记录任务开始执行
   */
  recordTaskStart(taskId: string, executorType: string): void {
    const metrics = this.getOrCreateMetrics(executorType)
    metrics.currentPending--
    metrics.currentRunning++

    this.taskStartTimes.set(taskId, Date.now())
    this.emitMetricUpdate(executorType, 'task_started')
  }

  /**
   * 记录任务完成
   */
  recordTaskComplete(taskId: string, executorType: string): void {
    const startTime = this.taskStartTimes.get(taskId)
    if (startTime) {
      const duration = Date.now() - startTime
      this.recordExecutionTime(executorType, duration)
      this.taskStartTimes.delete(taskId)
    }

    const metrics = this.getOrCreateMetrics(executorType)
    metrics.currentRunning--
    metrics.totalCompleted++

    this.emitMetricUpdate(executorType, 'task_completed')
  }

  /**
   * 获取执行器统计信息
   */
  getExecutorStats(executorType: string): ExecutorStats {
    const metrics = this.metrics.get(executorType)
    if (!metrics) return this.createEmptyStats()

    return {
      totalSubmitted: metrics.totalSubmitted,
      totalCompleted: metrics.totalCompleted,
      totalFailed: metrics.totalFailed,
      currentPending: metrics.currentPending,
      currentRunning: metrics.currentRunning,
      averageExecutionTime: this.calculateAverageTime(executorType),
      successRate: this.calculateSuccessRate(executorType),
      throughput: this.calculateThroughput(executorType)
    }
  }

  /**
   * 获取系统整体统计
   */
  getSystemStats(): SystemStats {
    const allExecutors = Array.from(this.metrics.keys())

    return {
      totalExecutors: allExecutors.length,
      totalTasks: this.getTotalTasks(),
      systemLoad: this.systemMetrics.getCurrentLoad(),
      memoryUsage: this.systemMetrics.getMemoryUsage(),
      averageWaitTime: this.calculateSystemAverageWaitTime(),
      resourceUtilization: this.calculateResourceUtilization()
    }
  }

  /**
   * 实时性能监控
   */
  startRealTimeMonitoring(): void {
    setInterval(() => {
      this.updateSystemMetrics()
      this.detectPerformanceIssues()
      this.emitSystemMetricsUpdate()
    }, 5000) // 每5秒更新一次
  }

  /**
   * 性能问题检测
   */
  private detectPerformanceIssues(): void {
    // 检测队列积压
    this.detectQueueBacklog()

    // 检测执行时间异常
    this.detectExecutionTimeAnomalies()

    // 检测资源利用率异常
    this.detectResourceUtilizationIssues()

    // 检测错误率异常
    this.detectErrorRateSpikes()
  }
}
```
